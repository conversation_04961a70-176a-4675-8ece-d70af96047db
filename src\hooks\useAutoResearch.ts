// 自动研究功能核心Hook
import { useState, useCallback, useRef, useEffect } from "react";
import { generateText } from "ai";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

// 自定义hooks导入
import useModelProvider from "@/hooks/useAiProvider";
import useDeepResearch from "@/hooks/useDeepResearch";
import { useTaskStore } from "@/store/task";
import { useSettingStore } from "@/store/setting";

// 类型和工具导入
import {
  AutoResearchConfig,
  ReflectionResult,
  EvaluationResult,
  isTimeExceeded,
  generateReflectionQuery,
  generateEvaluationQuery,
} from "@/types/auto-research";

import {
  generateReflectionPrompt,
  generateEvaluationPrompt,
  formatResearchFindings,
  parseJsonResponse,
  validateReflectionResult,
  validateEvaluationResult,
} from "@/utils/deep-research/auto-research-prompts";

import { getSystemPrompt, getSERPQueryOutputSchema } from "@/utils/deep-research/prompts";

/**
 * 自动研究功能Hook
 * 提供自动研究的核心功能，包括反思分析、质量评估和循环研究
 */
export function useAutoResearch() {
  const { t } = useTranslation();
  // 注意：不要使用useTaskStore()的返回值，因为它是快照
  // 总是使用useTaskStore.getState()来获取最新状态
  const getTaskStore = () => useTaskStore.getState();
  const settingStore = useSettingStore();
  const { createModelProvider, getModel } = useModelProvider();
  const { runSearchTask } = useDeepResearch();

  // 本地状态
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentStatus, setCurrentStatus] = useState<string>("");

  // 用于取消操作的引用
  const abortControllerRef = useRef<AbortController | null>(null);
  const isUnmountedRef = useRef(false);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      isUnmountedRef.current = true;
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // 监听运行状态变化，处理页面刷新后的状态恢复
  useEffect(() => {
    const taskStore = getTaskStore();
    const { isRunning } = taskStore.autoResearch.runtime;

    // 如果页面刷新后发现有运行状态但没有实际在运行，清理状态
    if (isRunning && !isProcessing) {
      console.warn("Detected stale auto research state, cleaning up...");
      taskStore.setAutoResearchRunning(false);
    }
  }, [getTaskStore().autoResearch.runtime.isRunning, isProcessing]);

  /**
   * 安全的状态更新函数
   * 只在组件未卸载时更新状态
   */
  const safeSetState = useCallback((updater: () => void) => {
    if (!isUnmountedRef.current) {
      updater();
    }
  }, []);

  /**
   * 错误处理函数
   */
  const handleError = useCallback((error: Error, context?: string) => {
    console.error(`Auto research error${context ? ` in ${context}` : ''}:`, error);

    // 根据错误类型提供不同的用户提示
    let errorMessage = error.message;
    let errorTitle = t("research.autoResearch.error", "自动研究出错");

    if (error.message.includes("timeout") || error.message.includes("超时")) {
      errorTitle = t("research.autoResearch.timeoutError", "请求超时");
      errorMessage = t("research.autoResearch.timeoutErrorDesc", "请求超时，请检查网络连接或稍后重试");
    } else if (error.message.includes("API") || error.message.includes("key")) {
      errorTitle = t("research.autoResearch.apiError", "API错误");
      errorMessage = t("research.autoResearch.apiErrorDesc", "API调用失败，请检查API配置");
    } else if (error.message.includes("Invalid") || error.message.includes("format")) {
      errorTitle = t("research.autoResearch.formatError", "数据格式错误");
      errorMessage = t("research.autoResearch.formatErrorDesc", "AI返回的数据格式不正确，请重试");
    }

    toast.error(errorTitle, {
      description: errorMessage,
      duration: 5000,
    });

    setIsProcessing(false);
    getTaskStore().setAutoResearchRunning(false);
  }, [t]);

  // 旧的函数已被删除，现在完全使用章节研究机制

  // 生成初始查询函数已被删除，现在使用章节研究机制

  // 等待搜索任务完成函数已被删除

  // 执行搜索任务函数已被删除



  /**
   * 启动自动研究
   * 现在默认使用章节研究模式，替换原有的反思思考机制
   */
  const startAutoResearch = useCallback(async () => {
    console.log("🎯 useAutoResearch.startAutoResearch 被调用");
    console.log("📊 isProcessing:", isProcessing);
    console.log("🏃 runtime.isRunning:", getTaskStore().autoResearch.runtime.isRunning);

    // 防止重复启动
    if (isProcessing || getTaskStore().autoResearch.runtime.isRunning) {
      console.log("⚠️ 已在运行中，显示警告");
      toast.warning(t("research.autoResearch.alreadyRunning", "自动研究已在进行中"));
      return;
    }

    console.log("🚀 调用章节研究功能");
    // 直接使用章节研究功能
    return await chapterResearch.startChapterResearch();
  }, [
    t,
    chapterResearch,
    isProcessing
  ]);

  /**
   * 停止自动研究
   * 现在只处理章节研究模式
   */
  const stopAutoResearch = useCallback(() => {
    // 停止章节研究
    chapterResearch.stopChapterResearch();
  }, [chapterResearch]);

  const taskStore = getTaskStore();
  return {
    // 状态 - 现在完全基于章节研究
    isProcessing: chapterResearch.isProcessing,
    currentStatus: chapterResearch.currentStatus,
    isRunning: chapterResearch.isProcessing,
    currentRound: 1, // 章节研究不使用轮次概念
    config: taskStore.autoResearch.config, // 保留配置以兼容UI
    showConfig: taskStore.autoResearch.showConfig,

    // 操作函数
    startAutoResearch,
    stopAutoResearch,

    // 章节研究功能
    chapterResearch: {
      isProcessing: chapterResearch.isProcessing,
      currentStatus: chapterResearch.currentStatus,
      startChapterResearch: chapterResearch.startChapterResearch,
      stopChapterResearch: chapterResearch.stopChapterResearch,
      getChapterList: chapterResearch.getChapterList,
      getChapterState: chapterResearch.getChapterState,
    },

    // 配置函数
    updateConfig: taskStore.updateAutoResearchConfig,
    setShowConfig: taskStore.setAutoResearchShowConfig,
    resetAutoResearch: taskStore.resetAutoResearch,
  };
}
