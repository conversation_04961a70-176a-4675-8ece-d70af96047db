// 自动研究功能核心Hook
import { useState, useCallback, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

// 自定义hooks导入
import { useChapterResearch } from "@/hooks/useChapterResearch";
import { useTaskStore } from "@/store/task";

// 类型和工具导入已简化，现在主要使用章节研究机制

/**
 * 自动研究功能Hook
 * 提供自动研究的核心功能，包括反思分析、质量评估和循环研究
 */
export function useAutoResearch() {
  const { t } = useTranslation();
  // 注意：不要使用useTaskStore()的返回值，因为它是快照
  // 总是使用useTaskStore.getState()来获取最新状态
  const getTaskStore = () => useTaskStore.getState();

  // 新的章节研究功能
  const chapterResearch = useChapterResearch();

  // 本地状态 - 简化版，主要功能已转移到章节研究
  const [isProcessing] = useState(false);

  // 用于取消操作的引用
  const abortControllerRef = useRef<AbortController | null>(null);
  const isUnmountedRef = useRef(false);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      isUnmountedRef.current = true;
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // 监听运行状态变化，处理页面刷新后的状态恢复
  useEffect(() => {
    const taskStore = getTaskStore();
    const { isRunning } = taskStore.autoResearch.runtime;

    // 如果页面刷新后发现有运行状态但没有实际在运行，清理状态
    if (isRunning && !isProcessing) {
      console.warn("Detected stale auto research state, cleaning up...");
      taskStore.setAutoResearchRunning(false);
    }
  }, [getTaskStore().autoResearch.runtime.isRunning, isProcessing]);

  // 安全状态更新已简化

  // 错误处理已简化，现在主要由章节研究机制处理

  // 旧的函数已被删除，现在完全使用章节研究机制

  // 生成初始查询函数已被删除，现在使用章节研究机制

  // 等待搜索任务完成函数已被删除

  // 执行搜索任务函数已被删除

  // 检查是否应该停止研究函数已被删除

  /**
   * 启动自动研究
   * 现在默认使用章节研究模式，替换原有的反思思考机制
   */
  const startAutoResearch = useCallback(async () => {
    console.log("🎯 useAutoResearch.startAutoResearch 被调用");
    console.log("📊 isProcessing:", isProcessing);
    console.log("🏃 runtime.isRunning:", getTaskStore().autoResearch.runtime.isRunning);

    // 防止重复启动
    if (isProcessing || getTaskStore().autoResearch.runtime.isRunning) {
      console.log("⚠️ 已在运行中，显示警告");
      toast.warning(t("research.autoResearch.alreadyRunning", "自动研究已在进行中"));
      return;
    }

    console.log("🚀 调用章节研究功能");
    // 直接使用章节研究功能
    return await chapterResearch.startChapterResearch();

    // 旧的反思思考机制已被章节研究模式替换
    // 以下代码保留用于向后兼容，但不再使用
    /*
    // 创建新的AbortController
    abortControllerRef.current = new AbortController();

    try {
      safeSetState(() => {
        setIsProcessing(true);
        setCurrentStatus(t("research.autoResearch.starting"));
      });

      const config = getTaskStore().autoResearch.config;
      const startTime = Date.now();

      // 验证配置
      if (config.maxRounds <= 0 || config.initialQueries <= 0) {
        throw new Error(t("research.autoResearch.invalidConfig", "配置参数无效"));
      }

      // 设置运行状态
      const taskStore = getTaskStore();
      taskStore.setAutoResearchRunning(true);
      taskStore.setAutoResearchCurrentRound(1);

      // 生成第一轮的搜索任务（基于报告计划和配置的初始查询数）
      safeSetState(() => {
        setCurrentStatus(t("research.autoResearch.generatingQueries", "生成搜索查询..."));
      });

      const initialQueries = await generateInitialQueries(config.initialQueries);
      if (!initialQueries || initialQueries.length === 0) {
        throw new Error(t("research.autoResearch.noQueriesGenerated", "未能生成搜索查询"));
      }

      console.log(`Generated ${initialQueries.length} initial queries:`, initialQueries.map(q => q.query));

      // 添加初始查询到任务列表
      console.log(`Before adding tasks, current task count: ${getTaskStore().tasks.length}`);

      const tasksToAdd = initialQueries.map(query => ({
        ...query,
        roundNumber: 1,
        taskType: "search" as const,
      }));

      console.log(`Tasks to add:`, tasksToAdd);

      // 尝试一次性添加所有任务，避免persist延迟问题
      console.log(`Attempting to add all tasks at once...`);

      // 获取当前状态
      const currentState = useTaskStore.getState();
      console.log(`Current state before adding:`, currentState.tasks.length);

      // 创建新的任务数组
      const allTasks = [...currentState.tasks, ...tasksToAdd];
      console.log(`New tasks array length:`, allTasks.length);

      // 使用setState一次性更新
      useTaskStore.setState(
        (state) => {
          console.log(`setState callback - current tasks:`, state.tasks.length);
          return {
            ...state,
            tasks: allTasks
          };
        }
      );

      console.log(`After setState call, checking state...`);
      const afterState = useTaskStore.getState();
      console.log(`State after setState:`, afterState.tasks.length);

      // 等待一下让状态更新
      await new Promise(resolve => setTimeout(resolve, 100));

      // 验证任务是否被正确添加
      const finalState = useTaskStore.getState();

      console.log(`Final task count: ${finalState.tasks.length}`);
      console.log(`All tasks:`, finalState.tasks.map(t => `${t.query} (round: ${t.roundNumber}, type: ${t.taskType}, state: ${t.state})`));

      const addedTasks = finalState.tasks.filter(task => task.roundNumber === 1);
      console.log(`Round 1 tasks: ${addedTasks.length}`, addedTasks.map(t => `${t.query} (${t.state})`));

      // 检查任务状态分布
      const tasksByState = addedTasks.reduce((acc, task) => {
        acc[task.state] = (acc[task.state] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      console.log(`Task states:`, tasksByState);

      // 如果任务仍然为空，说明有问题
      if (finalState.tasks.length === 0) {
        console.error(`Tasks are still empty after adding! This should not happen.`);
        throw new Error("Failed to add tasks to store");
      }

      let currentRound = 1;
      let shouldContinue = true;

      while (shouldContinue && currentRound <= config.maxRounds) {
        // 检查是否被取消
        if (abortControllerRef.current?.signal.aborted) {
          console.log("Auto research was cancelled");
          break;
        }

        safeSetState(() => {
          setCurrentStatus(t("research.autoResearch.processingRound", { round: currentRound }));
        });

        // 执行当前轮次的搜索任务
        const searchCompleted = await executeSearchTasks(currentRound);
        if (!searchCompleted) {
          console.warn(`Search tasks failed or timed out for round ${currentRound}`);
          break;
        }

        // 检查是否被取消
        if (abortControllerRef.current?.signal.aborted) {
          break;
        }

        // 执行反思分析
        const reflection = await performReflection(currentRound);
        if (!reflection) {
          console.warn(`Reflection failed for round ${currentRound}`);
          break;
        }

        // 检查是否被取消
        if (abortControllerRef.current?.signal.aborted) {
          break;
        }

        // 执行质量评估（传入反思结果和开始时间）
        const evaluation = await performEvaluation(currentRound, reflection, startTime);
        if (!evaluation) {
          console.warn(`Evaluation failed for round ${currentRound}`);
          break;
        }

        // 检查是否应该停止
        const { shouldStop, reason } = shouldStopResearch(
          config,
          currentRound,
          startTime,
          evaluation
        );

        if (shouldStop) {
          console.log(`Auto research stopped: ${reason}`);
          shouldContinue = false;
          break;
        }

        // 反思完成，继续进行评估阶段

        // 生成下一轮查询
        if (reflection.follow_up_queries.length > 0) {
          const nextRoundQueries = generateNextRoundQueries(reflection, currentRound + 1);

          // 添加到任务列表
          nextRoundQueries.forEach(query => {
            getTaskStore().addAutoResearchTask(query);
          });

          currentRound++;
          getTaskStore().setAutoResearchCurrentRound(currentRound);
        } else {
          console.log("No follow-up queries generated, stopping research");
          shouldContinue = false;
        }
      }

      // 完成自动研究
      if (!abortControllerRef.current?.signal.aborted) {
        safeSetState(() => {
          setCurrentStatus(t("research.autoResearch.completed"));
        });
        toast.success(t("research.autoResearch.completedSuccessfully"));
      }

    } catch (error) {
      if (!abortControllerRef.current?.signal.aborted) {
        handleError(error as Error, "startAutoResearch");
      }
    } finally {
      safeSetState(() => {
        setIsProcessing(false);
        setCurrentStatus("");
      });
      getTaskStore().setAutoResearchRunning(false);
      abortControllerRef.current = null;
    }
    */
  }, [
    t,
    chapterResearch,
    isProcessing
  ]);

  /**
   * 停止自动研究
   * 现在只处理章节研究模式
   */
  const stopAutoResearch = useCallback(() => {
    // 停止章节研究
    chapterResearch.stopChapterResearch();
  }, [chapterResearch]);

  const taskStore = getTaskStore();
  return {
    // 状态 - 现在完全基于章节研究
    isProcessing: chapterResearch.isProcessing,
    currentStatus: chapterResearch.currentStatus,
    isRunning: chapterResearch.isProcessing,
    currentRound: 1, // 章节研究不使用轮次概念
    config: taskStore.autoResearch.config, // 保留配置以兼容UI
    showConfig: taskStore.autoResearch.showConfig,

    // 操作函数
    startAutoResearch,
    stopAutoResearch,

    // 章节研究功能
    chapterResearch: {
      isProcessing: chapterResearch.isProcessing,
      currentStatus: chapterResearch.currentStatus,
      startChapterResearch: chapterResearch.startChapterResearch,
      stopChapterResearch: chapterResearch.stopChapterResearch,
      getChapterList: chapterResearch.getChapterList,
      getChapterState: chapterResearch.getChapterState,
    },

    // 配置函数
    updateConfig: taskStore.updateAutoResearchConfig,
    setShowConfig: taskStore.setAutoResearchShowConfig,
    resetAutoResearch: taskStore.resetAutoResearch,
  };
}
