// 自动研究功能核心Hook
import { useState, useCallback, useRef, useEffect } from "react";
import { generateText } from "ai";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

// 自定义hooks导入
import useModelProvider from "@/hooks/useAiProvider";
import useDeepResearch from "@/hooks/useDeepResearch";
import { useChapterResearch } from "@/hooks/useChapterResearch";
import { useTaskStore } from "@/store/task";
import { useSettingStore } from "@/store/setting";

// 类型和工具导入
// 旧的反思评估类型已被删除，现在使用章节研究
import {
  AutoResearchConfig,
  isTimeExceeded,
} from "@/types/auto-research";

// 旧的prompt生成函数已被删除，现在使用章节研究机制
import {
  formatResearchFindings,
  parseJsonResponse,
} from "@/utils/deep-research/auto-research-prompts";

import { getSystemPrompt, getSERPQueryOutputSchema } from "@/utils/deep-research/prompts";

/**
 * 自动研究功能Hook
 * 提供自动研究的核心功能，包括反思分析、质量评估和循环研究
 */
export function useAutoResearch() {
  const { t } = useTranslation();
  // 注意：不要使用useTaskStore()的返回值，因为它是快照
  // 总是使用useTaskStore.getState()来获取最新状态
  const getTaskStore = () => useTaskStore.getState();
  const settingStore = useSettingStore();
  const { createModelProvider, getModel } = useModelProvider();
  const { runSearchTask } = useDeepResearch();

  // 新的章节研究功能
  const chapterResearch = useChapterResearch();

  // 本地状态
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentStatus, setCurrentStatus] = useState<string>("");

  // 用于取消操作的引用
  const abortControllerRef = useRef<AbortController | null>(null);
  const isUnmountedRef = useRef(false);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      isUnmountedRef.current = true;
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // 监听运行状态变化，处理页面刷新后的状态恢复
  useEffect(() => {
    const taskStore = getTaskStore();
    const { isRunning } = taskStore.autoResearch.runtime;

    // 如果页面刷新后发现有运行状态但没有实际在运行，清理状态
    if (isRunning && !isProcessing) {
      console.warn("Detected stale auto research state, cleaning up...");
      taskStore.setAutoResearchRunning(false);
    }
  }, [getTaskStore().autoResearch.runtime.isRunning, isProcessing]);

  /**
   * 安全的状态更新函数
   * 只在组件未卸载时更新状态
   */
  const safeSetState = useCallback((updater: () => void) => {
    if (!isUnmountedRef.current) {
      updater();
    }
  }, []);

  /**
   * 错误处理函数
   */
  const handleError = useCallback((error: Error, context?: string) => {
    console.error(`Auto research error${context ? ` in ${context}` : ''}:`, error);

    // 根据错误类型提供不同的用户提示
    let errorMessage = error.message;
    let errorTitle = t("research.autoResearch.error", "自动研究出错");

    if (error.message.includes("timeout") || error.message.includes("超时")) {
      errorTitle = t("research.autoResearch.timeoutError", "请求超时");
      errorMessage = t("research.autoResearch.timeoutErrorDesc", "请求超时，请检查网络连接或稍后重试");
    } else if (error.message.includes("API") || error.message.includes("key")) {
      errorTitle = t("research.autoResearch.apiError", "API错误");
      errorMessage = t("research.autoResearch.apiErrorDesc", "API调用失败，请检查API配置");
    } else if (error.message.includes("Invalid") || error.message.includes("format")) {
      errorTitle = t("research.autoResearch.formatError", "数据格式错误");
      errorMessage = t("research.autoResearch.formatErrorDesc", "AI返回的数据格式不正确，请重试");
    }

    toast.error(errorTitle, {
      description: errorMessage,
      duration: 5000,
    });

    setIsProcessing(false);
    getTaskStore().setAutoResearchRunning(false);
  }, [t]);

  /**
   * 执行反思分析 - 已废弃，现在使用章节研究机制
   */
  const performReflection = useCallback(async (
    roundNumber: number
  ): Promise<any> => {
    // 旧的反思分析逻辑已被章节研究机制替换
    console.warn("performReflection is deprecated, use chapter research instead");
    return null;
  }, []);

  /**
   * 执行质量评估 - 已废弃，现在使用章节研究机制
   */
  const performEvaluation = useCallback(async (
    roundNumber: number,
    reflection: any,
    startTime: number
  ): Promise<any> => {
    // 旧的质量评估逻辑已被章节研究机制替换
    console.warn("performEvaluation is deprecated, use chapter research instead");
    return null;
  }, []);

  /**
   * 生成下一轮查询 - 已废弃，现在使用章节研究机制
   */
  const generateNextRoundQueries = useCallback((
    reflection: any,
    nextRoundNumber: number
  ) => {
    console.warn("generateNextRoundQueries is deprecated, use chapter research instead");
    return [];
  }, []);

  /**
   * 生成初始查询
   * 基于报告计划生成指定数量的搜索查询
   */
  const generateInitialQueries = useCallback(async (
    queryCount: number
  ): Promise<SearchTask[]> => {
    try {
      const { reportPlan } = getTaskStore();
      const { searchMode } = settingStore;
      const { thinkingModel } = getModel();

      if (!reportPlan) {
        throw new Error("No report plan available");
      }

      // 创建自定义的prompt，限制查询数量
      const customPrompt = `This is the report plan after user confirmation:
<PLAN>
${reportPlan}
</PLAN>

Based on previous report plan, generate EXACTLY ${queryCount} SERP queries to research the most important aspects of the topic. Make sure each query is unique and covers different aspects of the research plan.

**SEARCH MODE: ${searchMode}**

**IMPORTANT REQUIREMENTS:**
- Generate EXACTLY ${queryCount} queries (no more, no less)
- Each query should cover a different important aspect of the research plan
- Prioritize the most critical information needed for comprehensive research

**If searchMode is "bilingual":**
- Generate EXACTLY ${queryCount} queries total
- Mix Chinese and English queries to cover different aspects
- Aim for roughly equal distribution between Chinese and English
- Each query should be in the most appropriate language for that specific research aspect

**If searchMode is "chinese":**
- Generate only Chinese queries
- Queries should be natural and suitable for Chinese search engines

**If searchMode is "english":**
- Generate only English queries
- Queries should be natural and suitable for English search engines

You MUST respond in **JSON** matching this **JSON schema**:

\`\`\`json
${getSERPQueryOutputSchema()}
\`\`\``;

      // 调用AI生成查询
      const { text } = await generateText({
        model: await createModelProvider(thinkingModel),
        system: getSystemPrompt(),
        prompt: customPrompt,
      });

      // 解析JSON响应
      const queries = parseJsonResponse<any[]>(text);

      if (!queries || !Array.isArray(queries)) {
        throw new Error("Invalid queries format");
      }

      // 转换为SearchTask格式
      const searchTasks: SearchTask[] = queries.map(query => ({
        query: query.query,
        language: query.language as "chinese" | "english",
        groupId: query.groupId,
        researchGoal: query.researchGoal,
        learning: "",
        state: "unprocessed" as const,
        sources: [],
        images: [],
      }));

      return searchTasks;
    } catch (error) {
      console.error("Failed to generate initial queries:", error);
      handleError(error as Error, "generateInitialQueries");
      return [];
    }
  }, [settingStore, getModel, createModelProvider, handleError]);

  /**
   * 等待搜索任务完成
   * 等待指定轮次的所有搜索任务完成
   */
  const waitForSearchTasksCompletion = useCallback(async (
    roundNumber: number,
    maxWaitTime: number = 300000 // 默认5分钟超时
  ): Promise<boolean> => {
    const startTime = Date.now();
    let lastTaskCount = 0;
    let stableCount = 0;

    console.log(`Waiting for search tasks completion for round ${roundNumber}`);

    while (Date.now() - startTime < maxWaitTime) {
      // 检查是否被取消
      if (abortControllerRef.current?.signal.aborted) {
        return false;
      }

      const { tasks } = getTaskStore();
      const roundTasks = tasks.filter(
        task => task.roundNumber === roundNumber &&
                (!task.taskType || task.taskType === 'search')
      );

      // 如果没有该轮次的任务，返回true
      if (roundTasks.length === 0) {
        console.log(`No tasks found for round ${roundNumber}`);
        return true;
      }

      // 检查任务状态分布
      const unprocessed = roundTasks.filter(task => task.state === 'unprocessed').length;
      const processing = roundTasks.filter(task => task.state === 'processing').length;
      const completed = roundTasks.filter(task => task.state === 'completed').length;
      const failed = roundTasks.filter(task => task.state === 'failed').length;

      console.log(`Round ${roundNumber} tasks status: unprocessed=${unprocessed}, processing=${processing}, completed=${completed}, failed=${failed}`);

      // 检查是否所有任务都已完成
      const allCompleted = roundTasks.every(
        task => task.state === 'completed' || task.state === 'failed'
      );

      if (allCompleted) {
        console.log(`All tasks completed for round ${roundNumber}`);
        return true;
      }

      // 检查任务数量是否稳定（防止任务还在添加中）
      if (roundTasks.length === lastTaskCount) {
        stableCount++;
      } else {
        stableCount = 0;
        lastTaskCount = roundTasks.length;
      }

      // 如果任务数量稳定且有未处理的任务，可能需要手动触发
      if (stableCount > 5 && unprocessed > 0) {
        console.log(`Found ${unprocessed} unprocessed tasks, may need manual trigger`);
      }

      // 等待2秒后再检查
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // 超时
    console.warn(`Search tasks for round ${roundNumber} timed out`);
    return false;
  }, []);

  /**
   * 执行搜索任务
   * 启动指定轮次的搜索任务
   */
  const executeSearchTasks = useCallback(async (
    roundNumber: number
  ): Promise<boolean> => {
    try {
      const { tasks } = getTaskStore();

      // 详细调试信息
      const allRoundTasks = tasks.filter(task => task.roundNumber === roundNumber);
      console.log(`All tasks for round ${roundNumber}:`, allRoundTasks.map(t => `${t.query} (${t.state}, ${t.taskType || 'undefined'})`));

      const roundTasks = tasks.filter(
        task => task.roundNumber === roundNumber &&
                (!task.taskType || task.taskType === 'search') &&
                task.state === 'unprocessed'
      );

      console.log(`Executing search tasks for round ${roundNumber}, found ${roundTasks.length} unprocessed tasks out of ${allRoundTasks.length} total round tasks`);

      if (roundTasks.length === 0) {
        console.log(`No unprocessed search tasks found for round ${roundNumber}`);
        // 如果没有未处理的任务，但有其他状态的任务，可能已经在处理中
        if (allRoundTasks.length > 0) {
          console.log(`Found ${allRoundTasks.length} tasks for round ${roundNumber}, but none are unprocessed. Waiting for completion...`);
          const completed = await waitForSearchTasksCompletion(roundNumber);
          return completed;
        }
        return true;
      }

      // 启动搜索任务
      console.log(`Starting search tasks for round ${roundNumber}:`, roundTasks.map(t => t.query));
      await runSearchTask(roundTasks);

      // 等待任务完成
      console.log(`Waiting for search tasks completion for round ${roundNumber}`);
      const completed = await waitForSearchTasksCompletion(roundNumber);

      console.log(`Search tasks for round ${roundNumber} completed: ${completed}`);
      return completed;
    } catch (error) {
      console.error(`Failed to execute search tasks for round ${roundNumber}:`, error);
      return false;
    }
  }, [runSearchTask, waitForSearchTasksCompletion]);

  /**
   * 检查是否应该停止研究
   */
  const shouldStopResearch = useCallback((
    config: AutoResearchConfig,
    currentRound: number,
    startTime: number,
    evaluation?: EvaluationResult
  ): { shouldStop: boolean; reason: string } => {
    console.log("=== shouldStopResearch Debug ===");
    console.log("currentRound:", currentRound, "maxRounds:", config.maxRounds);
    console.log("qualityThreshold:", config.qualityThreshold);
    if (evaluation) {
      console.log("evaluation.coverage_completeness_score:", evaluation.coverage_completeness_score);
      console.log("evaluation.should_continue:", evaluation.should_continue);
      console.log("evaluation.resource_constraint_reason:", evaluation.resource_constraint_reason);
    } else {
      console.log("evaluation is null/undefined");
    }

    // 检查最大轮数 (10表示无限制)
    if (config.maxRounds < 10 && currentRound >= config.maxRounds) {
      console.log("Stopping: max rounds reached");
      return { shouldStop: true, reason: "max_rounds_reached" };
    }

    // 检查时间限制 (30分钟表示无限制)
    if (config.maxDuration < 30 && isTimeExceeded(startTime, config.maxDuration)) {
      console.log("Stopping: time exceeded");
      return { shouldStop: true, reason: "time_exceeded" };
    }

    // 检查质量阈值（使用覆盖完整性分数）
    if (evaluation && evaluation.coverage_completeness_score >= config.qualityThreshold) {
      console.log("Stopping: coverage threshold reached", evaluation.coverage_completeness_score, ">=", config.qualityThreshold);
      return { shouldStop: true, reason: "coverage_threshold_reached" };
    }

    // 检查评估决策
    if (evaluation && !evaluation.should_continue) {
      console.log("Stopping: evaluation says should not continue");
      return { shouldStop: true, reason: evaluation.resource_constraint_reason };
    }

    console.log("Continuing research");
    return { shouldStop: false, reason: "" };
  }, []);

  /**
   * 启动自动研究
   * 现在默认使用章节研究模式，替换原有的反思思考机制
   */
  const startAutoResearch = useCallback(async () => {
    // 防止重复启动
    if (isProcessing || getTaskStore().autoResearch.runtime.isRunning) {
      toast.warning(t("research.autoResearch.alreadyRunning", "自动研究已在进行中"));
      return;
    }

    // 直接使用章节研究功能
    return await chapterResearch.startChapterResearch();

    // 旧的反思思考机制已被章节研究模式替换
    // 以下代码保留用于向后兼容，但不再使用
    /*
    // 创建新的AbortController
    abortControllerRef.current = new AbortController();

    try {
      safeSetState(() => {
        setIsProcessing(true);
        setCurrentStatus(t("research.autoResearch.starting"));
      });

      const config = getTaskStore().autoResearch.config;
      const startTime = Date.now();

      // 验证配置
      if (config.maxRounds <= 0 || config.initialQueries <= 0) {
        throw new Error(t("research.autoResearch.invalidConfig", "配置参数无效"));
      }

      // 设置运行状态
      const taskStore = getTaskStore();
      taskStore.setAutoResearchRunning(true);
      taskStore.setAutoResearchCurrentRound(1);

      // 生成第一轮的搜索任务（基于报告计划和配置的初始查询数）
      safeSetState(() => {
        setCurrentStatus(t("research.autoResearch.generatingQueries", "生成搜索查询..."));
      });

      const initialQueries = await generateInitialQueries(config.initialQueries);
      if (!initialQueries || initialQueries.length === 0) {
        throw new Error(t("research.autoResearch.noQueriesGenerated", "未能生成搜索查询"));
      }

      console.log(`Generated ${initialQueries.length} initial queries:`, initialQueries.map(q => q.query));

      // 添加初始查询到任务列表
      console.log(`Before adding tasks, current task count: ${getTaskStore().tasks.length}`);

      const tasksToAdd = initialQueries.map(query => ({
        ...query,
        roundNumber: 1,
        taskType: "search" as const,
      }));

      console.log(`Tasks to add:`, tasksToAdd);

      // 尝试一次性添加所有任务，避免persist延迟问题
      console.log(`Attempting to add all tasks at once...`);

      // 获取当前状态
      const currentState = useTaskStore.getState();
      console.log(`Current state before adding:`, currentState.tasks.length);

      // 创建新的任务数组
      const allTasks = [...currentState.tasks, ...tasksToAdd];
      console.log(`New tasks array length:`, allTasks.length);

      // 使用setState一次性更新
      useTaskStore.setState(
        (state) => {
          console.log(`setState callback - current tasks:`, state.tasks.length);
          return {
            ...state,
            tasks: allTasks
          };
        }
      );

      console.log(`After setState call, checking state...`);
      const afterState = useTaskStore.getState();
      console.log(`State after setState:`, afterState.tasks.length);

      // 等待一下让状态更新
      await new Promise(resolve => setTimeout(resolve, 100));

      // 验证任务是否被正确添加
      const finalState = useTaskStore.getState();

      console.log(`Final task count: ${finalState.tasks.length}`);
      console.log(`All tasks:`, finalState.tasks.map(t => `${t.query} (round: ${t.roundNumber}, type: ${t.taskType}, state: ${t.state})`));

      const addedTasks = finalState.tasks.filter(task => task.roundNumber === 1);
      console.log(`Round 1 tasks: ${addedTasks.length}`, addedTasks.map(t => `${t.query} (${t.state})`));

      // 检查任务状态分布
      const tasksByState = addedTasks.reduce((acc, task) => {
        acc[task.state] = (acc[task.state] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      console.log(`Task states:`, tasksByState);

      // 如果任务仍然为空，说明有问题
      if (finalState.tasks.length === 0) {
        console.error(`Tasks are still empty after adding! This should not happen.`);
        throw new Error("Failed to add tasks to store");
      }

      let currentRound = 1;
      let shouldContinue = true;

      while (shouldContinue && currentRound <= config.maxRounds) {
        // 检查是否被取消
        if (abortControllerRef.current?.signal.aborted) {
          console.log("Auto research was cancelled");
          break;
        }

        safeSetState(() => {
          setCurrentStatus(t("research.autoResearch.processingRound", { round: currentRound }));
        });

        // 执行当前轮次的搜索任务
        const searchCompleted = await executeSearchTasks(currentRound);
        if (!searchCompleted) {
          console.warn(`Search tasks failed or timed out for round ${currentRound}`);
          break;
        }

        // 检查是否被取消
        if (abortControllerRef.current?.signal.aborted) {
          break;
        }

        // 执行反思分析
        const reflection = await performReflection(currentRound);
        if (!reflection) {
          console.warn(`Reflection failed for round ${currentRound}`);
          break;
        }

        // 检查是否被取消
        if (abortControllerRef.current?.signal.aborted) {
          break;
        }

        // 执行质量评估（传入反思结果和开始时间）
        const evaluation = await performEvaluation(currentRound, reflection, startTime);
        if (!evaluation) {
          console.warn(`Evaluation failed for round ${currentRound}`);
          break;
        }

        // 检查是否应该停止
        const { shouldStop, reason } = shouldStopResearch(
          config,
          currentRound,
          startTime,
          evaluation
        );

        if (shouldStop) {
          console.log(`Auto research stopped: ${reason}`);
          shouldContinue = false;
          break;
        }

        // 反思完成，继续进行评估阶段

        // 生成下一轮查询
        if (reflection.follow_up_queries.length > 0) {
          const nextRoundQueries = generateNextRoundQueries(reflection, currentRound + 1);

          // 添加到任务列表
          nextRoundQueries.forEach(query => {
            getTaskStore().addAutoResearchTask(query);
          });

          currentRound++;
          getTaskStore().setAutoResearchCurrentRound(currentRound);
        } else {
          console.log("No follow-up queries generated, stopping research");
          shouldContinue = false;
        }
      }

      // 完成自动研究
      if (!abortControllerRef.current?.signal.aborted) {
        safeSetState(() => {
          setCurrentStatus(t("research.autoResearch.completed"));
        });
        toast.success(t("research.autoResearch.completedSuccessfully"));
      }

    } catch (error) {
      if (!abortControllerRef.current?.signal.aborted) {
        handleError(error as Error, "startAutoResearch");
      }
    } finally {
      safeSetState(() => {
        setIsProcessing(false);
        setCurrentStatus("");
      });
      getTaskStore().setAutoResearchRunning(false);
      abortControllerRef.current = null;
    }
    */
  }, [
    t,
    chapterResearch
  ]);

  /**
   * 停止自动研究
   * 现在只处理章节研究模式
   */
  const stopAutoResearch = useCallback(() => {
    // 停止章节研究
    chapterResearch.stopChapterResearch();
  }, [chapterResearch]);

  const taskStore = getTaskStore();
  return {
    // 状态 - 现在完全基于章节研究
    isProcessing: chapterResearch.isProcessing,
    currentStatus: chapterResearch.currentStatus,
    isRunning: chapterResearch.isProcessing,
    currentRound: 1, // 章节研究不使用轮次概念
    config: taskStore.autoResearch.config, // 保留配置以兼容UI
    showConfig: taskStore.autoResearch.showConfig,

    // 操作函数
    startAutoResearch,
    stopAutoResearch,

    // 保留旧函数以兼容现有代码，但标记为已废弃
    performReflection: () => Promise.resolve(null), // 已废弃
    performEvaluation: () => Promise.resolve(null), // 已废弃

    // 章节研究功能
    chapterResearch: {
      isProcessing: chapterResearch.isProcessing,
      currentStatus: chapterResearch.currentStatus,
      startChapterResearch: chapterResearch.startChapterResearch,
      stopChapterResearch: chapterResearch.stopChapterResearch,
      getChapterList: chapterResearch.getChapterList,
      getChapterState: chapterResearch.getChapterState,
    },

    // 配置函数
    updateConfig: taskStore.updateAutoResearchConfig,
    setShowConfig: taskStore.setAutoResearchShowConfig,
    resetAutoResearch: taskStore.resetAutoResearch,
  };
}
