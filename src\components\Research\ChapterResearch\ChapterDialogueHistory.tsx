import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { ChevronDown, ChevronRight, MessageSquare, Brain, Lightbulb, Search } from "lucide-react";
import { Discussion } from "@/utils/deep-research/auto-research-prompts";

interface ChapterDialogueHistoryProps {
  chapterGoal: string;
  chapterIndex: number;
  totalChapters: number;
  discussions: Discussion[];
  currentRound: number;
  isActive: boolean;
  isRunning: boolean;
  currentAgent?: 'Alpha' | 'Beta';
  currentPhase?: 'HIGH_LEVEL' | 'QUERY';
  highLevelLoopCount?: number;
  queryLoopCount?: number;
  maxHighLevelLoops?: number;
  maxQueryLoops?: number;
  roundThreshold?: number;
  maxRounds?: number;
  chapterStatus?: 'pending' | 'in_progress' | 'completed';
}

export default function ChapterDialogueHistory({
  chapterGoal,
  chapterIndex,
  totalChapters,
  discussions,
  currentRound,
  isActive,
  isRunning,
  currentAgent,
  currentPhase = 'HIGH_LEVEL',
  highLevelLoopCount = 0,
  queryLoopCount = 0,
  maxHighLevelLoops = 3,
  maxQueryLoops = 3,
  roundThreshold = 2,
  maxRounds = 5,
  chapterStatus = 'pending'
}: ChapterDialogueHistoryProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [showRawLog, setShowRawLog] = useState(false);

  // 按轮次分组讨论
  const discussionsByRound = discussions.reduce((acc, discussion) => {
    if (!acc[discussion.round]) {
      acc[discussion.round] = [];
    }
    acc[discussion.round].push(discussion);
    return acc;
  }, {} as Record<number, Discussion[]>);

  // 获取章节状态显示
  const getChapterStatusInfo = () => {
    switch (chapterStatus) {
      case 'pending':
        return { icon: '⏳', text: '待启动', color: 'bg-gray-100 text-gray-800' };
      case 'in_progress':
        return { icon: '🔄', text: '进行中', color: 'bg-blue-100 text-blue-800' };
      case 'completed':
        return { icon: '✅', text: '已完成', color: 'bg-green-100 text-green-800' };
      default:
        return { icon: '❓', text: '未知', color: 'bg-gray-100 text-gray-800' };
    }
  };

  // 获取进度
  const getProgress = () => {
    if (!isActive) return 0;
    return Math.min((currentRound / maxRounds) * 100, 100);
  };

  // 获取阶段显示信息
  const getPhaseInfo = () => {
    if (currentPhase === 'HIGH_LEVEL') {
      return {
        name: '高层级讨论',
        icon: '💡',
        color: 'bg-blue-100 text-blue-800',
        progress: highLevelLoopCount / maxHighLevelLoops,
        count: `${highLevelLoopCount}/${maxHighLevelLoops}`
      };
    } else {
      return {
        name: '查询规划',
        icon: '🔍',
        color: 'bg-purple-100 text-purple-800',
        progress: queryLoopCount / maxQueryLoops,
        count: `${queryLoopCount}/${maxQueryLoops}`
      };
    }
  };

  // 检查是否接近强制推进
  const isNearForced = () => {
    if (currentPhase === 'HIGH_LEVEL') {
      return highLevelLoopCount >= maxHighLevelLoops - 1;
    } else {
      return queryLoopCount >= maxQueryLoops - 1;
    }
  };

  // 检查是否接近章节结束
  const isNearChapterEnd = () => {
    return currentRound >= roundThreshold;
  };

  // 获取智能体颜色
  const getAgentColor = (agentId: string) => {
    return agentId === 'Alpha' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800';
  };

  // 获取智能体图标
  const getAgentIcon = (agentId: string) => {
    return agentId === 'Alpha' ? '🤖' : '🧠';
  };

  // 获取动作颜色
  const getActionColor = (actionName: string) => {
    switch (actionName) {
      case 'HIGH_LEVEL_PROPOSE':
      case 'HIGH_LEVEL_AGREE':
        return 'bg-blue-50 text-blue-700';
      case 'QUERY_PROPOSE':
      case 'EXECUTE_QUERIES':
        return 'bg-purple-50 text-purple-700';
      case 'CHAPTER_END_PROPOSE':
      case 'CHAPTER_END_AGREE':
        return 'bg-green-50 text-green-700';
      default:
        return 'bg-gray-50 text-gray-700';
    }
  };

  // 获取动作图标
  const getActionIcon = (actionName: string) => {
    switch (actionName) {
      case 'HIGH_LEVEL_PROPOSE':
        return <Lightbulb className="h-4 w-4" />;
      case 'HIGH_LEVEL_AGREE':
        return <Brain className="h-4 w-4" />;
      case 'QUERY_PROPOSE':
        return <Search className="h-4 w-4" />;
      case 'EXECUTE_QUERIES':
        return <MessageSquare className="h-4 w-4" />;
      default:
        return <MessageSquare className="h-4 w-4" />;
    }
  };

  // 渲染收缩状态
  const renderCollapsedState = () => {
    const statusInfo = getChapterStatusInfo();
    return (
      <div className="flex items-center gap-2">
        <Badge variant="secondary" className={statusInfo.color}>
          {statusInfo.icon} {statusInfo.text}
        </Badge>
        {isActive && isRunning && (
          <Badge variant="default">第{currentRound}轮 - {currentAgent}思考中</Badge>
        )}
      </div>
    );
  };

  // 渲染载荷内容
  const renderPayloadContent = (action: any) => {
    if (!action.payload) return null;

    const { payload } = action;

    if (payload.queries && Array.isArray(payload.queries)) {
      return (
        <div className="border-l-2 pl-4 border-purple-200">
          <p className="font-medium text-purple-600 text-xs uppercase tracking-wider">查询提议</p>
          <div className="mt-2 space-y-2">
            {payload.queries.map((query: any, idx: number) => (
              <div key={idx} className="bg-purple-50 p-3 rounded-md">
                <p className="font-medium text-purple-800">{idx + 1}. {query.query}</p>
                <p className="text-sm text-purple-600 mt-1">目标: {query.researchGoal}</p>
              </div>
            ))}
          </div>
        </div>
      );
    }

    if (payload.proposal) {
      return (
        <div className="border-l-2 pl-4 border-blue-200">
          <p className="font-medium text-blue-600 text-xs uppercase tracking-wider">提议内容</p>
          <p className="mt-1 text-blue-800">{payload.proposal}</p>
        </div>
      );
    }

    if (payload.agreement) {
      return (
        <div className="border-l-2 pl-4 border-green-200">
          <p className="font-medium text-green-600 text-xs uppercase tracking-wider">同意理由</p>
          <p className="mt-1 text-green-800">{payload.agreement}</p>
        </div>
      );
    }

    if (payload.justification) {
      return (
        <div className="border-l-2 pl-4 border-orange-200">
          <p className="font-medium text-orange-600 text-xs uppercase tracking-wider">理由说明</p>
          <p className="mt-1 text-orange-800">{payload.justification}</p>
        </div>
      );
    }

    return null;
  };

  return (
    <Card className="w-full border-dashed border-2 p-4">
      <CardHeader className="cursor-pointer" onClick={() => setIsExpanded(!isExpanded)}>
        <div className="flex justify-between items-center">
          <CardTitle className="flex items-center gap-2">
            {isExpanded ? <ChevronDown className="h-5 w-5" /> : <ChevronRight className="h-5 w-5" />}
            <MessageSquare className="h-5 w-5" />
            第{chapterIndex}章对话历史
          </CardTitle>
          {!isExpanded && renderCollapsedState()}
        </div>
        {isExpanded && (
          <CardDescription className="pt-2">
            {chapterGoal}
          </CardDescription>
        )}
      </CardHeader>

      {isExpanded && (
        <CardContent className="space-y-6">
          {/* 章节状态和进度 */}
          <div className="space-y-4">
            {/* 章节状态 */}
            <div className="flex items-center justify-between">
              <h4 className="text-lg font-semibold">章节状态</h4>
              <div className="flex gap-2">
                <Badge variant="outline">
                  第 {chapterIndex} / {totalChapters} 章
                </Badge>
                <Badge variant="secondary" className={getChapterStatusInfo().color}>
                  {getChapterStatusInfo().icon} {getChapterStatusInfo().text}
                </Badge>
              </div>
            </div>

            {/* 进度显示 */}
            {isActive && (
              <div>
                <div className="flex items-center justify-between mb-2">
                  <h5 className="font-medium">讨论进度</h5>
                  <div className="flex gap-2">
                    <Badge variant="outline">
                      第 {currentRound}/{maxRounds} 轮
                    </Badge>
                    {isNearChapterEnd() && (
                      <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                        ⚠️ 接近结束判断
                      </Badge>
                    )}
                  </div>
                </div>
                <p className="text-sm text-gray-500 mb-2">
                  当前: {currentAgent} • {isRunning ? '思考中...' : '等待中'}
                </p>
                <Progress value={getProgress()} className="w-full" />
              </div>
            )}

            {/* 阶段信息 */}
            {isActive && (
              <div>
                <div className="flex items-center justify-between mb-2">
                  <h5 className="font-medium">当前阶段</h5>
                  {isNearForced() && (
                    <Badge variant="secondary" className="bg-red-100 text-red-800">
                      ⚡ 即将强制推进
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-3">
                  <Badge className={getPhaseInfo().color}>
                    {getPhaseInfo().icon} {getPhaseInfo().name}
                  </Badge>
                  <span className="text-sm text-gray-500">
                    循环: {getPhaseInfo().count}
                  </span>
                  <div className="flex-1">
                    <Progress
                      value={getPhaseInfo().progress * 100}
                      className="w-full h-2"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          {isActive && <Separator className="my-6" />}

          {/* 章节开始提示 */}
          {isActive && discussions.length === 0 && (
            <div className="text-center py-8 bg-blue-50 rounded-lg border-2 border-dashed border-blue-200">
              <div className="text-4xl mb-2">🚀</div>
              <h3 className="text-lg font-semibold text-blue-800 mb-2">第{chapterIndex}章研究启动</h3>
              <p className="text-blue-600">{chapterGoal}</p>
              <p className="text-sm text-blue-500 mt-2">Alpha 和 Beta 智能体即将开始对话...</p>
            </div>
          )}

          {/* 对话历史 */}
          {discussions.length > 0 && (
            <div>
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-lg font-semibold">Alpha-Beta 对话</h4>
                <Button variant="link" size="sm" onClick={() => setShowRawLog(!showRawLog)} className="text-xs">
                  {showRawLog ? '隐藏原始日志' : '</> 显示原始日志'}
                </Button>
              </div>
              <ScrollArea className="h-[400px] pr-4 border bg-gray-50/50 rounded-md p-4">
                {showRawLog ? (
                  <pre className="whitespace-pre-wrap text-xs text-gray-600 bg-black p-4 rounded-md text-white font-mono">
                    {discussions.map(d =>
                      `[第${d.round}轮-第${d.turn}次] ${d.agentId}\n思考: ${d.thought}\n动作: ${d.action.actionName}\n\n`
                    ).join('')}
                  </pre>
                ) : (
                  <div className="space-y-6">
                    {Object.entries(discussionsByRound)
                      .sort(([a], [b]) => parseInt(a) - parseInt(b))
                      .map(([roundNum, roundDiscussions]) => (
                        <div key={roundNum} className="border rounded-lg p-4 bg-white shadow-sm">
                          <div className="flex items-center gap-2 mb-4 pb-2 border-b">
                            <Badge variant="outline" className="font-semibold">
                              第 {roundNum} 轮讨论
                            </Badge>
                            <span className="text-xs text-gray-500">
                              {roundDiscussions.length} 次对话
                            </span>
                          </div>
                          
                          <div className="space-y-3">
                            {roundDiscussions.map((discussion, index) => (
                              <div key={index} className="border-l-4 border-gray-200 pl-4">
                                <div className="flex items-center gap-2 mb-2">
                                  {getActionIcon(discussion.action.actionName)}
                                  <Badge variant="outline" className={`font-semibold ${getAgentColor(discussion.agentId)}`}>
                                    {getAgentIcon(discussion.agentId)} {discussion.agentId}
                                  </Badge>
                                  <Badge variant="secondary" className={getActionColor(discussion.action.actionName)}>
                                    {discussion.action.actionName}
                                  </Badge>
                                  <span className="text-xs text-gray-500 ml-auto">
                                    第{discussion.turn}次
                                  </span>
                                </div>
                                
                                <div className="space-y-2 text-sm">
                                  <div className="border-l-2 pl-3 border-gray-200">
                                    <p className="font-medium text-gray-500 text-xs uppercase tracking-wider">思考过程</p>
                                    <p className="mt-1 text-gray-800">{discussion.thought}</p>
                                  </div>

                                  {renderPayloadContent(discussion.action)}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                  </div>
                )}
              </ScrollArea>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
}
