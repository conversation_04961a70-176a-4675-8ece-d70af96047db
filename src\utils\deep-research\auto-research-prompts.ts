// 自动研究功能专用Prompts
import type { ReflectionResult, EvaluationResult } from "../../types/auto-research";

/**
 * 反思分析Prompt - 专注当前轮次内容质量分析
 * 基于上轮查询建议分析当轮搜索的改进程度和新发现
 */
export const autoResearchReflectionPrompt = `You are an expert research analyst evaluating the current round search results for "{research_topic}".

Your primary task is to analyze how well the current round addressed previous knowledge gaps and identify new research directions.

## Analysis Framework:

**1. Improvement Assessment:**
- Compare current round findings with previous round queries/goals
- Evaluate how well the identified knowledge gaps were addressed
- Assess the quality and depth of new information gathered

**2. Content Quality Analysis:**
- Rate the accuracy, credibility, and recency of current round sources
- Evaluate technical depth and practical applicability
- Identify any contradictory or unclear information

**3. Knowledge Gap Identification:**
- Based on current findings, identify remaining knowledge gaps
- Focus on areas that would significantly enhance research completeness
- Consider recent developments and implementation specifics

## Research Context:
**Research Topic:** {research_topic}
**Original Research Plan:** {research_plan}
**Previous Round Queries:** {previous_queries}
**Previous Strategic Guidance:** {previous_recommendation}
**Current Round Search Results:** {current_findings}
**Current Date:** {current_date}

## Output Requirements:
Provide your analysis in the following JSON format only (no additional text):

\`\`\`json
{
  "content_quality_score": number,
  "knowledge_completeness_score": number,
  "improvement_analysis": "specific analysis of how current round improved upon previous gaps",
  "key_discoveries": ["key finding 1", "key finding 2", "key finding 3"],
  "knowledge_gaps": ["specific remaining gap 1", "specific remaining gap 2"],
  "follow_up_queries": [
    {
      "query": "targeted search query",
      "language": "chinese" | "english",
      "researchGoal": "specific research objective for this query"
    }
  ]
}
\`\`\`

## Guidelines:
- **content_quality_score**: Rate the accuracy, depth, and credibility of current round results (0-1)
- **knowledge_completeness_score**: Rate how well current round addresses the research objectives (0-1)
- **improvement_analysis**: Describe specific improvements made compared to previous round gaps
- **key_discoveries**: List the most valuable insights and findings from current round (as array)
- **knowledge_gaps**: List remaining specific, actionable gaps that need further research
- **follow_up_queries**: Generate 1-5 targeted queries addressing the most critical remaining gaps
- Focus on content analysis, not resource constraints or strategic decisions
- Each query must include "query", "language", and "researchGoal" fields
- If no significant gaps remain, set "follow_up_queries" to empty array`;

/**
 * 质量评估Prompt - 基于反思结果和全局信息做出战略决策
 * 提供资源状态报告和专业的研究战略建议，职责分离清晰
 */
export const autoResearchEvaluationPrompt = `You are an expert research strategist. Your primary role is to determine if research on "{research_topic}" should continue **based solely on the quality and completeness of the research content.**

Your task is to provide a strategic research assessment.

## Your Tasks:

**Strategic Research Assessment:**
- Analyze current reflection results and research quality trends
- Review global research coverage based on all rounds' discoveries
- Evaluate the significance of remaining knowledge gaps
- Assess the potential value of proposed follow-up queries
- Provide professional judgment on research completeness and direction
- Focus purely on research content value

## Research Context:
**Research Topic:** {research_topic}
**Original Research Plan:** {research_plan}
**Current Round Reflection:** {current_reflection}
**All SERP Query Keywords:** {all_query_keywords}
**All Rounds Key Discoveries:** {all_rounds_discoveries}
**Previous Coverage Scores:** {previous_coverage_scores}

## Output Requirements:
Provide your evaluation in the following JSON format only (no additional text):

\`\`\`json
{
  "should_continue": boolean,
  "strategic_recommendation": "professional judgment on whether research should continue based on content quality",
  "coverage_completeness_score": number,
  "score_change_justification": "explanation for any significant change from previous coverage scores"
}
\`\`\`

## Guidelines:

**For 'strategic_recommendation':**
- Base your professional judgment purely on research content and quality
- **Strategic Assessment Dimensions:**
  - Knowledge gap significance (critical vs minor gaps)
  - Research quality trend (improving vs declining across rounds)
- Provide clear recommendation with specific reasoning
- Focus on research completeness and content value

**Coverage Completeness Score Calculation:**
- Evaluate how well all SERP queries and discoveries cover the original research plan
- Consider breadth (different aspects covered) and depth (detail level achieved)
- Score 0.0-1.0 where 1.0 means comprehensive coverage of all planned research areas
- **Scoring Reference Points:**
  - 0.0-0.3: Basic framework understanding, major gaps remain
  - 0.3-0.6: Core concepts covered, implementation details needed
  - 0.6-0.8: Comprehensive coverage, minor gaps in specific areas
  - 0.8-1.0: Near-complete coverage, only edge cases or optimizations remain
- **Historical Consistency Requirements:**
  - **Single-round change limit**: ±0.25 maximum unless major breakthrough/discovery
  - **Change >0.15**: Must provide detailed justification in score_change_justification
  - **Change >0.25**: Requires exceptional circumstances (fundamental new insights, discovery of previous errors)
  - If no previous scores exist, set score_change_justification to "首次评估"
- Base calculation on content analysis

**Final Decision Logic:**
- **should_continue**: Your professional recommendation based ONLY on research content assessment
  - true: if research content reveals significant gaps or valuable directions remain
  - false: if research content appears sufficiently comprehensive
- **Example Scenarios:**
  - High quality research with critical remaining gaps: should_continue = true
  - Comprehensive coverage achieved with minor gaps only: should_continue = false
  - Quality scores declining with repetitive information: should_continue = false`;

/**
 * 生成反思分析的完整prompt
 */
export function generateReflectionPrompt(
  researchTopic: string,
  researchPlan: string,
  currentFindings: string,
  previousQueries: string = '无',
  previousRecommendation: string = '无',
  currentDate: string = new Date().toLocaleDateString()
): string {
  return autoResearchReflectionPrompt
    .replace('{research_topic}', researchTopic)
    .replace('{research_plan}', researchPlan)
    .replace('{current_findings}', currentFindings)
    .replace('{previous_queries}', previousQueries)
    .replace('{previous_recommendation}', previousRecommendation)
    .replace('{current_date}', currentDate);
}

/**
 * 生成质量评估的完整prompt
 */
export function generateEvaluationPrompt(
  researchTopic: string,
  researchPlan: string,
  currentReflection: string,
  allQueryKeywords: string,
  allRoundsDiscoveries: string,
  previousCoverageScores: string = '无历史评分'
): string {
  return autoResearchEvaluationPrompt
    .replace('{research_topic}', researchTopic)
    .replace('{research_plan}', researchPlan)
    .replace('{current_reflection}', currentReflection)
    .replace('{all_query_keywords}', allQueryKeywords)
    .replace('{all_rounds_discoveries}', allRoundsDiscoveries)
    .replace('{previous_coverage_scores}', previousCoverageScores);
}

/**
 * 格式化研究发现为文本
 * 将搜索任务结果格式化为用于prompt的文本
 */
export function formatResearchFindings(tasks: any[]): string {
  if (!tasks || tasks.length === 0) {
    return '暂无研究发现';
  }

  return tasks
    .filter(task => task.learning && task.learning.trim())
    .map((task) => {
      const roundInfo = task.roundNumber ? `[第${task.roundNumber}轮] ` : '';
      const languageInfo = task.language ? ` (${task.language === 'chinese' ? '中文' : '英文'})` : '';

      return `## ${roundInfo}${task.query}${languageInfo}

**研究目标**: ${task.researchGoal || '未指定'}

**研究发现**:
${task.learning}

**来源数量**: ${task.sources?.length || 0}
**图片数量**: ${task.images?.length || 0}

---`;
    })
    .join('\n\n');
}

/**
 * 格式化所有轮次的研究发现
 * 按轮次组织研究发现，用于最终评估
 */
export function formatAllRoundsFindings(tasks: any[]): string {
  if (!tasks || tasks.length === 0) {
    return '暂无研究发现';
  }

  // 按轮次分组
  const roundGroups: { [key: number]: any[] } = {};
  
  tasks.forEach(task => {
    const roundNumber = task.roundNumber || 1;
    if (!roundGroups[roundNumber]) {
      roundGroups[roundNumber] = [];
    }
    roundGroups[roundNumber].push(task);
  });

  // 格式化每个轮次
  const roundSummaries = Object.keys(roundGroups)
    .sort((a, b) => parseInt(a) - parseInt(b))
    .map(roundKey => {
      const roundNumber = parseInt(roundKey);
      const roundTasks = roundGroups[roundNumber];
      
      const searchTasks = roundTasks.filter(task => !task.taskType || task.taskType === 'search');
      const reflectionTasks = roundTasks.filter(task => task.taskType === 'reflection');
      const evaluationTasks = roundTasks.filter(task => task.taskType === 'evaluation');

      let roundSummary = `# 第${roundNumber}轮研究\n\n`;
      
      // 搜索任务
      if (searchTasks.length > 0) {
        roundSummary += `## 搜索查询 (${searchTasks.length}个)\n\n`;
        searchTasks.forEach(task => {
          const languageInfo = task.language ? ` (${task.language === 'chinese' ? '中文' : '英文'})` : '';
          roundSummary += `### ${task.query}${languageInfo}\n`;
          roundSummary += `**研究目标**: ${task.researchGoal || '未指定'}\n`;
          roundSummary += `**发现**: ${task.learning || '无内容'}\n\n`;
        });
      }

      // 反思分析
      if (reflectionTasks.length > 0) {
        roundSummary += `## 反思分析\n\n`;
        reflectionTasks.forEach(task => {
          roundSummary += `${task.learning || '无反思内容'}\n\n`;
        });
      }

      // 质量评估
      if (evaluationTasks.length > 0) {
        roundSummary += `## 质量评估\n\n`;
        evaluationTasks.forEach(task => {
          roundSummary += `${task.learning || '无评估内容'}\n\n`;
        });
      }

      return roundSummary + '---\n\n';
    });

  return roundSummaries.join('');
}

/**
 * 解析JSON响应
 * 安全地解析AI返回的JSON响应
 */
export function parseJsonResponse<T>(response: string): T | null {
  try {
    // 尝试提取JSON代码块
    const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
    const jsonString = jsonMatch ? jsonMatch[1] : response;
    
    return JSON.parse(jsonString.trim()) as T;
  } catch (error) {
    console.error('Failed to parse JSON response:', error);
    console.error('Response content:', response);
    return null;
  }
}

/**
 * 验证反思结果
 */
export function validateReflectionResult(result: any): result is ReflectionResult {
  return (
    typeof result === 'object' &&
    typeof result.content_quality_score === 'number' &&
    typeof result.knowledge_completeness_score === 'number' &&
    typeof result.improvement_analysis === 'string' &&
    Array.isArray(result.key_discoveries) &&
    Array.isArray(result.knowledge_gaps) &&
    Array.isArray(result.follow_up_queries) &&
    result.follow_up_queries.every((q: any) =>
      typeof q === 'object' &&
      typeof q.query === 'string' &&
      typeof q.language === 'string' &&
      typeof q.researchGoal === 'string'
    )
  );
}

/**
 * 验证评估结果
 */
export function validateEvaluationResult(result: any): result is EvaluationResult {
  return (
    typeof result === 'object' &&
    typeof result.should_continue === 'boolean' &&
    typeof result.strategic_recommendation === 'string' &&
    typeof result.coverage_completeness_score === 'number' &&
    typeof result.score_change_justification === 'string'
  );
}

// ===== 章节研究相关类型定义 =====

/**
 * 章节信息
 */
export interface ChapterInfo {
  id: string;
  title: string;
  summary: string;
  goal: string;
}

/**
 * 智能体动作
 */
export interface AgentAction {
  actionName: 'CONTINUE_DISCUSSION' | 'EXECUTE_QUERIES' | 'CONCLUDE_CHAPTER';
  payload: {
    queries?: ConsensusQuery[];
    rationale?: string;
    justification?: string;
  };
}

/**
 * 讨论记录
 */
export interface Discussion {
  agentId: 'Alpha' | 'Beta';
  round: number;
  turn: number;
  thought: string;
  speech: string;
  action: AgentAction;
}

/**
 * 辩论历史
 */
export interface DebateHistory {
  discussions: Discussion[];
  currentRound: number;
  totalTurns: number;
}

/**
 * 共识查询
 */
export interface ConsensusQuery {
  query: string;
  language: 'chinese' | 'english';
  researchGoal: string;
}

/**
 * 共识结果
 */
export interface ConsensusResult {
  hasConsensus: boolean;
  queries: ConsensusQuery[];
  reasoning: string;
}

/**
 * 章节状态
 */
export interface ChapterState {
  id: string;
  goal: string;
  status: 'pending' | 'in_progress' | 'completed';
  debateHistory: DebateHistory;
  consensus: ConsensusResult | null;
  knowledgeSummary: string;
  queryResults: any[];
}

// ===== 章节研究相关函数 =====

/**
 * 从计划中解析章节
 */
export function parseChaptersFromPlan(reportPlan: string): ChapterInfo[] {
  // 解析普通plan格式（文本）
  const chapters: ChapterInfo[] = [];
  const lines = reportPlan.split('\n').filter(line => line.trim());

  let currentChapter = 1;
  for (const line of lines) {
    const trimmedLine = line.trim();

    // 匹配各种章节标题格式
    const chapterPatterns = [
      /^(\d+)\.\s*(.+)$/,           // "1. 标题"
      /^第(\d+)[章节部分]\s*[：:]\s*(.+)$/,  // "第1章：标题"
      /^[#]+\s*(.+)$/,              // "# 标题" 或 "## 标题"
      /^[-*]\s*(.+)$/,              // "- 标题" 或 "* 标题"
    ];

    for (const pattern of chapterPatterns) {
      const match = trimmedLine.match(pattern);
      if (match) {
        const title = match[2] || match[1];
        if (title && title.length > 3) { // 过滤太短的标题
          chapters.push({
            id: `chapter_${currentChapter}`,
            title: title.trim(),
            summary: '',
            goal: `深入研究和分析：${title.trim()}`
          });
          currentChapter++;
          break;
        }
      }
    }
  }

  return chapters;
}

/**
 * 生成章节研究Prompt
 */
export function generateChapterResearchPrompt(
  agentId: 'Alpha' | 'Beta',
  chapterGoal: string,
  discussionHistory: string,
  knowledgeSummary: string
): string {
  const agentRole = agentId === 'Alpha' ? '主导研究者' : '批判分析者';

  return `你是一个${agentRole}，正在进行章节式深度研究。

## 当前章节目标
${chapterGoal}

## 已有知识总结
${knowledgeSummary || '暂无已有知识'}

## 讨论历史
${discussionHistory || '这是第一次发言'}

## 你的任务
作为${agentRole}，你需要：
1. 深入思考当前章节的研究目标
2. 基于已有讨论和知识，提出你的观点
3. 决定下一步行动

## 响应格式
请严格按照以下JSON格式响应：

\`\`\`json
{
  "thought": "你的深入思考过程",
  "speech": "你要表达的观点和建议",
  "action": {
    "actionName": "CONTINUE_DISCUSSION|EXECUTE_QUERIES|CONCLUDE_CHAPTER",
    "payload": {
      "queries": [{"query": "查询内容", "language": "chinese|english", "researchGoal": "查询目标"}],
      "rationale": "继续讨论的理由",
      "justification": "结束章节的理由"
    }
  }
}
\`\`\`

## 行动说明
- CONTINUE_DISCUSSION: 继续与对方讨论，需要提供rationale
- EXECUTE_QUERIES: 执行搜索查询，需要提供queries数组
- CONCLUDE_CHAPTER: 结束当前章节，需要提供justification`;
}
