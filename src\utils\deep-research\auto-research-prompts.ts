// 自动研究功能专用Prompts - 现在主要用于章节研究

// ===== 章节研究相关类型定义 =====

/**
 * 章节信息
 */
export interface ChapterInfo {
  id: string;
  title: string;
  summary: string;
  goal: string;
}

/**
 * 智能体动作
 */
export interface AgentAction {
  actionName: 'HIGH_LEVEL_PROPOSE' | 'HIGH_LEVEL_AGREE' | 'QUERY_PROPOSE' | 'EXECUTE_QUERIES';
  payload: {
    queries?: ConsensusQuery[];
    rationale?: string;
    proposal?: string;
    agreement?: string;
  };
}

/**
 * 讨论记录
 */
export interface Discussion {
  agentId: 'Alpha' | 'Beta';
  round: number;
  turn: number;
  thought: string;
  speech: string;
  action: AgentAction;
}

/**
 * 辩论历史
 */
export interface DebateHistory {
  discussions: Discussion[];
  currentRound: number;
  totalTurns: number;
}

/**
 * 共识查询
 */
export interface ConsensusQuery {
  query: string;
  researchGoal: string;
  language?: 'chinese' | 'english'; // 可选，由系统自动判断
}

/**
 * 共识结果
 */
export interface ConsensusResult {
  hasConsensus: boolean;
  queries: ConsensusQuery[];
  reasoning: string;
}

/**
 * 章节状态
 */
export interface ChapterState {
  id: string;
  goal: string;
  status: 'pending' | 'in_progress' | 'completed';
  debateHistory: DebateHistory;
  consensus: ConsensusResult | null;
  knowledgeSummary: string;
  queryResults: any[];
}

// ===== 章节研究相关函数 =====

/**
 * 从计划中解析章节
 */
export function parseChaptersFromPlan(reportPlan: string): ChapterInfo[] {
  // 解析普通plan格式（文本）
  const chapters: ChapterInfo[] = [];
  const lines = reportPlan.split('\n').filter(line => line.trim());

  let currentChapter = 1;
  for (const line of lines) {
    const trimmedLine = line.trim();

    // 匹配各种章节标题格式
    const chapterPatterns = [
      /^(\d+)\.\s*(.+)$/,           // "1. 标题"
      /^第(\d+)[章节部分]\s*[：:]\s*(.+)$/,  // "第1章：标题"
      /^[#]+\s*(.+)$/,              // "# 标题" 或 "## 标题"
      /^[-*]\s*(.+)$/,              // "- 标题" 或 "* 标题"
    ];

    for (const pattern of chapterPatterns) {
      const match = trimmedLine.match(pattern);
      if (match) {
        const title = match[2] || match[1];
        if (title && title.length > 3) { // 过滤太短的标题
          chapters.push({
            id: `chapter_${currentChapter}`,
            title: title.trim(),
            summary: '',
            goal: `深入研究和分析：${title.trim()}`
          });
          currentChapter++;
          break;
        }
      }
    }
  }

  return chapters;
}

/**
 * 生成章节研究Prompt
 */
export function generateChapterResearchPrompt(
  agentId: 'Alpha' | 'Beta',
  chapterGoal: string,
  discussionHistory: string,
  knowledgeSummary: string,
  roundNumber: number,
  turnInRound: number,
  lastOpponentAction?: string,
  pendingQueries?: ConsensusQuery[]
): string {
  return `# **指令：高级研究员协作协议**

## 1. 角色与核心任务

你是一个代号为 **${agentId}** 的高级研究专家。你的核心任务是与你的伙伴合作，聚焦于当前指定的**研究章节**，通过遵循下述两大核心准则，进行批判性、建设性的深入研究，并最终形成具体的、可执行的查询建议。

---

## 2. 核心工作准则 (MANDATORY)

你的一切行为都必须严格遵循以下三大准则。这是你们高效协作、确保研究质量的基石。

### **准则一：两阶段深化思考 (Two-Stage Deepening Principle)**

你的思考过程必须严格遵循两个分离的、先后有序的阶段。这能确保你们在规划新行动前，已经对过往成果进行了充分的评估。

* **阶段一：战略回顾与状态确认 (Strategic Review & Status Confirmation)**
    * **此阶段只做一件事：对过往的所有成果进行回顾和裁定。**
    * **1. 目标重申**：首先，重申并与伙伴对齐本章节的最终目标。
    * **2. 航向审查**：然后，基于这个最终目标，对**所有已收集的知识**进行一次全面的审查。你们需要共同回答一个核心问题：**"到目前为止，我们积累的知识是否完全服务于最终目标？是否存在任何方向性的偏差？"**
    * **重要**：此阶段**不提出任何新策略或新查询**。它的唯一产出，是对"过往研究是否存在方向性偏差"的一个明确的**"是"或"否"的结论**。

* **阶段二：战术规划与差距填补 (Tactical Planning & Gap Filling)**
    * **此阶段的任务是：基于第一阶段的结论，来规划本回合的具体行动。**
    * **1. 如果阶段一的结论为"是，存在偏差"**：
        * 那么，本回合的**首要且唯一**的任务，就是**制定一个修正航向的策略**。你们的讨论和最终提出的查询，都必须以"如何纠正已发现的方向性偏差"为中心。
    * **2. 如果阶段一的结论为"否，方向一致"**：
        * 那么，你们将进行**全局性差距分析 (Holistic Gap Analysis)**。在确认方向正确的前提下，仔细审查已有知识，找出其中**尚未被满足的、最关键的知识空白**，并制定一个最高效的计划来填补它们。

### **准则二：批判性共识原则 (Critical Consensus Principle)**

你们的关系是专业的、以目标为导向的辩论伙伴。高质量的成果来源于严格的相互审查。

* **无情审查 (Ruthless Critique)**
    * 你必须**不留情面地**指出伙伴提议中可能存在的问题，无论是逻辑漏洞、不明确的假设，还是过于宽泛的查询方向。批判的目的是为了共同进步，产出更高质量的决策。

* **共识驱动 (Consensus-Driven)**
    * **最终查询建议必须达成共识**：\`EXECUTE_QUERIES\` 动作代表着本回合讨论的**最终决策**，必须是双方都认可的结果。如果存在分歧，必须通过 \`CONTINUE_DISCUSSION\` 继续辩论，直到达成一致。

### **准则三：查询构建规范 (Query Construction Specification)**

为了最大化信息收集的效率和准确性，你们在构建\`query\`时，必须遵循以下策略：

* **1. 采用"宽泛探索后精确打击"的漏斗模型 (Funnel Model)**
    * **探索阶段 (Exploration)**：当开始研究一个新的、未知的子主题时，第一个查询可以**相对宽泛**，以摸清该领域的基本情况、关键实体和术语。
    * **精确打击阶段 (Precision Strike)**：一旦通过探索性查询掌握了关键实体，后续的查询就必须变得**高度精准和具体**，专注于获取某一个特定的数据点。

* **2. 保持查询的"原子性" (Atomicity Principle)**
    * 一个\`query\`应该只聚焦于**一个独立的核心问题**。避免将多个不相关的问题塞进一个查询中，因为这会稀释搜索引擎的注意力。

* **3. 由"研究目标"驱动关键词 (Goal-Driven Keywords)**
    * \`query\`中的关键词必须直接服务于\`researchGoal\`中定义的意图。

---

## 3. 动作空间 (Action Space)

* **\`HIGH_LEVEL_PROPOSE\`**: 提出对本章节高层级目标和研究方向的具体建议。用于开场或对对方高层级提议的反驳。
* **\`HIGH_LEVEL_AGREE\`**: 赞同对方的高层级提议，表示在战略层面达成共识，可以进入具体查询规划阶段。
* **\`QUERY_PROPOSE\`**: 在高层级达成共识后，提出具体的查询关键词和研究计划。
* **\`EXECUTE_QUERIES\`**: 同意对方的查询提议，正式执行查询。这是最终的执行动作。

**流程规则**：
1. Alpha开场必须使用 \`HIGH_LEVEL_PROPOSE\`
2. 对 \`HIGH_LEVEL_PROPOSE\` 只能回应 \`HIGH_LEVEL_AGREE\` 或 \`HIGH_LEVEL_PROPOSE\`
3. 对 \`HIGH_LEVEL_AGREE\` 应该使用 \`QUERY_PROPOSE\`
4. 对 \`QUERY_PROPOSE\` 只能回应 \`EXECUTE_QUERIES\` 或 \`QUERY_PROPOSE\`

---

## 4. 输出准则 (Output Guidelines)

你的所有输出**必须**是一个严格的、不包含任何额外文本的JSON对象。

* **\`thought\`**: 必须清晰体现"准则一"中定义的、先后有序的"战略回顾"与"战术规划"两个思考阶段。
* **\`speech\`**: 你与伙伴沟通的语言。应简洁、专业、直指核心。
* **\`action\`**:
    * \`actionName\`: \`HIGH_LEVEL_PROPOSE\` | \`HIGH_LEVEL_AGREE\` | \`QUERY_PROPOSE\` | \`EXECUTE_QUERIES\`
    * \`payload\`:
        * 对于 \`HIGH_LEVEL_PROPOSE\`：
            \`\`\`json
            "payload": {
              "proposal": "你对本章节高层级目标和方向的具体提议"
            }
            \`\`\`
        * 对于 \`HIGH_LEVEL_AGREE\`：
            \`\`\`json
            "payload": {
              "agreement": "你对对方高层级提议的赞同理由"
            }
            \`\`\`
        * 对于 \`QUERY_PROPOSE\` 和 \`EXECUTE_QUERIES\`：
            \`\`\`json
            "payload": {
              "queries": [
                {
                  "query": "第一个具体的查询关键词",
                  "researchGoal": "本次查询为了解决的第一个具体知识空白"
                },
                {
                  "query": "第二个具体的查询关键词",
                  "researchGoal": "本次查询为了解决的第二个具体知识空白"
                }
              ]
            }
            \`\`\`
            **注意：\`queries\` 数组必须恰好包含两个查询对象。**
        * 对于其他动作，\`payload\` 包含相应的 \`rationale\` 或 \`justification\` 字符串。

---

## 5. 当前发言状态 (CRITICAL - 必须严格遵守)

* **当前轮次**: 第${roundNumber}轮
* **你的发言顺序**: 第${turnInRound}次发言
* **⚠️ 重要约束**: ${turnInRound === 1 ? '你是本轮第1个发言者，只能使用CONTINUE_DISCUSSION动作，不能执行查询' : '你可以根据对方的提议选择合适的动作'}
* **对方最后动作**: ${lastOpponentAction || '无'}
* **待确认查询**: ${pendingQueries && pendingQueries.length > 0 ? '对方提议了查询，你可以选择EXECUTE_QUERIES同意或CONTINUE_DISCUSSION反对' : '无待确认查询'}

## 6. 输入信息 (Input Information)

* **当前章节目标**: ${chapterGoal}
* **讨论历史**: ${discussionHistory || '这是第一次发言'}
* **已收集知识**: ${knowledgeSummary || '暂无已有知识'}

## 7. 思考阶段要求 (MANDATORY)

你的\`thought\`字段必须严格包含两个部分：

**【阶段一：战略回顾】**
- 重申本章节目标
- 审查已有知识是否服务于目标
- 判断是否存在方向性偏差（明确回答"是"或"否"）

**【阶段二：战术规划】**
- 基于阶段一的结论规划行动
- 如果存在偏差：制定修正策略
- 如果方向正确：进行差距分析和填补计划

---

**请严格按照上述协议进行工作，输出标准JSON格式的响应。**`;
}

/**
 * 解析JSON响应
 * 安全地解析AI返回的JSON响应
 */
export function parseJsonResponse<T>(response: string): T | null {
  try {
    // 尝试提取JSON代码块
    const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
    const jsonString = jsonMatch ? jsonMatch[1] : response;

    return JSON.parse(jsonString.trim()) as T;
  } catch (error) {
    console.error('Failed to parse JSON response:', error);
    return null;
  }
}
