// 自动研究功能专用Prompts

/**
 * 章节解析相关类型定义
 */
export interface ChapterInfo {
  id: string;
  title: string;
  summary: string;
  goal: string;
}

/**
 * 从研究计划中解析章节信息
 * 支持两种格式：对抗plan（结构化）和普通plan（文本）
 */
export function parseChaptersFromPlan(reportPlan: string): ChapterInfo[] {
  try {
    // 尝试解析对抗plan格式（JSON结构）
    const adversarialPlan = JSON.parse(reportPlan);
    if (Array.isArray(adversarialPlan) && adversarialPlan.length > 0) {
      return adversarialPlan.map((section, index) => ({
        id: `chapter_${index + 1}`,
        title: section.section_title || `第${index + 1}章`,
        summary: section.summary || '',
        goal: `研究并分析${section.section_title}：${section.summary}`
      }));
    }
  } catch (e) {
    // 不是JSON格式，按普通plan处理
  }

  // 解析普通plan格式（文本）
  const chapters: ChapterInfo[] = [];
  const lines = reportPlan.split('\n').filter(line => line.trim());

  let currentChapter = 1;
  for (const line of lines) {
    const trimmedLine = line.trim();

    // 匹配各种章节标题格式
    const chapterPatterns = [
      /^(\d+)\.\s*(.+)$/,           // "1. 标题"
      /^第(\d+)[章节部分]\s*[：:]\s*(.+)$/,  // "第1章：标题"
      /^[#]+\s*(.+)$/,              // "# 标题" 或 "## 标题"
      /^[-*]\s*(.+)$/,              // "- 标题" 或 "* 标题"
    ];

    for (const pattern of chapterPatterns) {
      const match = trimmedLine.match(pattern);
      if (match) {
        const title = match[2] || match[1];
        if (title && title.length > 3) { // 过滤太短的标题
          chapters.push({
            id: `chapter_${currentChapter}`,
            title: title.trim(),
            summary: '',
            goal: `深入研究和分析：${title.trim()}`
          });
          currentChapter++;
          break;
        }
      }
    }
  }

  // 如果没有解析到章节，创建一个默认章节
  if (chapters.length === 0) {
    chapters.push({
      id: 'chapter_1',
      title: '综合研究',
      summary: '基于研究计划进行全面分析',
      goal: '根据研究计划进行全面的信息收集和分析'
    });
  }

  return chapters;
}

/**
 * 反思分析Prompt - 专注当前轮次内容质量分析
 * 基于上轮查询建议分析当轮搜索的改进程度和新发现
 */
export const autoResearchReflectionPrompt = `You are an expert research analyst evaluating the current round search results for "{research_topic}".

Your primary task is to analyze how well the current round addressed previous knowledge gaps and identify new research directions.

## Analysis Framework:

**1. Improvement Assessment:**
- Compare current round findings with previous round queries/goals
- Evaluate how well the identified knowledge gaps were addressed
- Assess the quality and depth of new information gathered

**2. Content Quality Analysis:**
- Rate the accuracy, credibility, and recency of current round sources
- Evaluate technical depth and practical applicability
- Identify any contradictory or unclear information

**3. Knowledge Gap Identification:**
- Based on current findings, identify remaining knowledge gaps
- Focus on areas that would significantly enhance research completeness
- Consider recent developments and implementation specifics

## Research Context:
**Research Topic:** {research_topic}
**Original Research Plan:** {research_plan}
**Previous Round Queries:** {previous_queries}
**Previous Strategic Guidance:** {previous_recommendation}
**Current Round Search Results:** {current_findings}
**Current Date:** {current_date}

## Output Requirements:
Provide your analysis in the following JSON format only (no additional text):

\`\`\`json
{
  "content_quality_score": number,
  "knowledge_completeness_score": number,
  "improvement_analysis": "specific analysis of how current round improved upon previous gaps",
  "key_discoveries": ["key finding 1", "key finding 2", "key finding 3"],
  "knowledge_gaps": ["specific remaining gap 1", "specific remaining gap 2"],
  "follow_up_queries": [
    {
      "query": "targeted search query",
      "language": "chinese" | "english",
      "researchGoal": "specific research objective for this query"
    }
  ]
}
\`\`\`

## Guidelines:
- **content_quality_score**: Rate the accuracy, depth, and credibility of current round results (0-1)
- **knowledge_completeness_score**: Rate how well current round addresses the research objectives (0-1)
- **improvement_analysis**: Describe specific improvements made compared to previous round gaps
- **key_discoveries**: List the most valuable insights and findings from current round (as array)
- **knowledge_gaps**: List remaining specific, actionable gaps that need further research
- **follow_up_queries**: Generate 1-5 targeted queries addressing the most critical remaining gaps
- Focus on content analysis, not resource constraints or strategic decisions
- Each query must include "query", "language", and "researchGoal" fields
- If no significant gaps remain, set "follow_up_queries" to empty array`;

/**
 * 质量评估Prompt - 基于反思结果和全局信息做出战略决策
 * 提供资源状态报告和专业的研究战略建议，职责分离清晰
 */
export const autoResearchEvaluationPrompt = `You are an expert research strategist. Your primary role is to determine if research on "{research_topic}" should continue **based solely on the quality and completeness of the research content.**

Your task is to provide a strategic recommendation and a factual resource report.

## Your Tasks:

**1. Factual Resource Report:**
- Factually report the current resource usage without making judgments
- State the current round out of the maximum allowed
- State the elapsed time out of the time limit
- State the current coverage score in relation to the quality threshold
- Provide a clear, factual summary in Chinese format

**2. Strategic Research Assessment:**
- Analyze current reflection results and research quality trends
- Review global research coverage based on all rounds' discoveries
- Evaluate the significance of remaining knowledge gaps
- Assess the potential value of proposed follow-up queries
- Provide professional judgment on research completeness and direction
- Focus purely on research content value, ignoring resource constraints

## Research Context:
**Research Topic:** {research_topic}
**Original Research Plan:** {research_plan}
**Current Round Reflection:** {current_reflection}
**All SERP Query Keywords:** {all_query_keywords}
**All Rounds Key Discoveries:** {all_rounds_discoveries}

**Resource Constraints:**
- Current Round: {current_round} / Maximum: {max_rounds}
- Time Usage: {elapsed_minutes} / {max_duration} minutes
- Quality Threshold: {quality_threshold}

## Output Requirements:
Provide your evaluation in the following JSON format only (no additional text):

\`\`\`json
{
  "should_continue": boolean,
  "resource_constraint_reason": "objective status report: 轮次: X/Y, 时间: A/B分钟, 质量: C%/D%",
  "strategic_recommendation": "professional judgment on whether research should continue based on content quality",
  "coverage_completeness_score": number
}
\`\`\`

## Guidelines:

**For 'resource_constraint_reason':**
- Provide an objective status report of ALL resource data
- Simply state the current status without making stop/continue judgments
- Format: "轮次: X/Y, 时间: A/B分钟, 质量: C%/D%"
- Do NOT include conclusions like "资源允许继续" or "必须停止"
- Examples:
  - "轮次: 2/5, 时间: 3.2/30.0分钟, 质量: 65%/80%"
  - "轮次: 1/3, 时间: 2.1/5.0分钟, 质量: 85%/40%"
  - "轮次: 5/5, 时间: 15.0/30.0分钟, 质量: 85%/80%"

**For 'strategic_recommendation':**
- Base your professional judgment purely on research content and quality
- **Strategic Assessment Dimensions:**
  - Knowledge gap significance (critical vs minor gaps)
  - Research quality trend (improving vs declining across rounds)
  - Diminishing returns (new insights vs repetitive information)
  - Follow-up query potential (high-value vs low-value research directions)
- Provide clear recommendation with specific reasoning
- Focus on research completeness and content value, not resource efficiency

**Coverage Completeness Score Calculation:**
- Evaluate how well all SERP queries and discoveries cover the original research plan
- Consider breadth (different aspects covered) and depth (detail level achieved)
- Score 0.0-1.0 where 1.0 means comprehensive coverage of all planned research areas
- Base calculation on content analysis, not resource usage

**Final Decision Logic:**
- **should_continue**: Your professional recommendation based ONLY on research content assessment
  - true: if research content reveals significant gaps or valuable directions remain
  - false: if research content appears sufficiently comprehensive
- **Example Scenarios:**
  - High quality research with critical remaining gaps: should_continue = true
  - Comprehensive coverage achieved with minor gaps only: should_continue = false
  - Quality scores declining with repetitive information: should_continue = false
- Note: Resource constraints (rounds, time, quality thresholds) will be handled separately by the system`;

/**
 * 生成反思分析的完整prompt
 */
export function generateReflectionPrompt(
  researchTopic: string,
  researchPlan: string,
  currentFindings: string,
  previousQueries: string = '无',
  previousRecommendation: string = '无',
  currentDate: string = new Date().toLocaleDateString()
): string {
  return autoResearchReflectionPrompt
    .replace('{research_topic}', researchTopic)
    .replace('{research_plan}', researchPlan)
    .replace('{current_findings}', currentFindings)
    .replace('{previous_queries}', previousQueries)
    .replace('{previous_recommendation}', previousRecommendation)
    .replace('{current_date}', currentDate);
}

/**
 * 生成质量评估的完整prompt
 */
export function generateEvaluationPrompt(
  researchTopic: string,
  researchPlan: string,
  currentReflection: string,
  allQueryKeywords: string,
  allRoundsDiscoveries: string,
  currentRound: number,
  maxRounds: number,
  maxDuration: number,
  elapsedMinutes: number,
  qualityThreshold: number = 0.7
): string {
  return autoResearchEvaluationPrompt
    .replace('{research_topic}', researchTopic)
    .replace('{research_plan}', researchPlan)
    .replace('{current_reflection}', currentReflection)
    .replace('{all_query_keywords}', allQueryKeywords)
    .replace('{all_rounds_discoveries}', allRoundsDiscoveries)
    .replace('{current_round}', currentRound.toString())
    .replace('{max_rounds}', maxRounds.toString())
    .replace('{max_duration}', maxDuration.toString())
    .replace('{elapsed_minutes}', elapsedMinutes.toFixed(1))
    .replace('{quality_threshold}', qualityThreshold.toString());
}

/**
 * 章节式研究相关类型定义
 */
export interface AgentAction {
  actionName: 'CONTINUE_DISCUSSION' | 'EXECUTE_QUERIES' | 'CONCLUDE_CHAPTER';
  payload: {
    rationale?: string;
    queries?: ConsensusQuery[];
    justification?: string;
  };
}

export interface ConsensusQuery {
  query: string;
  language: "chinese" | "english";
  researchGoal: string;
}

export interface Discussion {
  round: number;
  turn: number;
  agentId: 'Alpha' | 'Beta';
  thought: string;
  speech: string;
  action: AgentAction;
  timestamp: number;
}

export interface DebateHistory {
  discussions: Discussion[];
  currentRound: number;
  totalTurns: number;
}

export interface ConsensusResult {
  agreedQueries: ConsensusQuery[];
  justification: string;
  timestamp: number;
  participatingAgents: string[];
}

export interface ChapterState {
  id: string;
  goal: string;
  status: 'pending' | 'in_progress' | 'completed';
  debateHistory: DebateHistory;
  consensus: ConsensusResult | null;
  knowledgeSummary: string;
  queryResults: any[];
}

/**
 * 双AI辩论机制的Prompt模板
 * 基于重构目标文档中的设计
 */
export const chapterResearchAgentPrompt = `# **指令：高级研究员协作协议**

## 1. 角色与核心任务

你是一个代号为 **\`{agent_name}\`** 的高级研究专家。你的核心任务是与你的伙伴合作，聚焦于当前指定的**研究章节 (\`current_chapter_goal\`)**，通过遵循下述两大核心准则，进行批判性、建设性的深入研究，并最终形成具体的、可执行的查询建议。

---

## 2. 核心工作准则 (MANDATORY)

你的一切行为都必须严格遵循以下三大准则。这是你们高效协作、确保研究质量的基石。

### **准则一：两阶段深化思考 (Two-Stage Deepening Principle)**

你的思考过程必须严格遵循两个分离的、先后有序的阶段。这能确保你们在规划新行动前，已经对过往成果进行了充分的评估。

* **阶段一：战略回顾与状态确认 (Strategic Review & Status Confirmation)**
    * **此阶段只做一件事：对过往的所有成果进行回顾和裁定。**
    * **1. 目标重申**：首先，重申并与伙伴对齐本章节的最终目标 (\`current_chapter_goal\`)。
    * **2. 航向审查**：然后，基于这个最终目标，对**所有已收集的知识 (\`knowledge_summary\`)** 进行一次全面的审查。你们需要共同回答一个核心问题：**"到目前为止，我们积累的知识是否完全服务于最终目标？是否存在任何方向性的偏差？"**
    * **重要**：此阶段**不提出任何新策略或新查询**。它的唯一产出，是对"过往研究是否存在方向性偏差"的一个明确的**"是"或"否"的结论**。

* **阶段二：战术规划与差距填补 (Tactical Planning & Gap Filling)**
    * **此阶段的任务是：基于第一阶段的结论，来规划本回合的具体行动。**
    * **1. 如果阶段一的结论为"是，存在偏差"**：
        * 那么，本回合的**首要且唯一**的任务，就是**制定一个修正航向的策略**。你们的讨论和最终提出的查询，都必须以"如何纠正已发现的方向性偏差"为中心。
    * **2. 如果阶段一的结论为"否，方向一致"**：
        * 那么，你们将进行**全局性差距分析 (Holistic Gap Analysis)**。在确认方向正确的前提下，仔细审查\`knowledge_summary\`，找出其中**尚未被满足的、最关键的知识空白**，并制定一个最高效的计划来填补它们。

### **准则二：批判性共识原则 (Critical Consensus Principle)**

你们的关系是专业的、以目标为导向的辩论伙伴。高质量的成果来源于严格的相互审查。

* **无情审查 (Ruthless Critique)**
    * 你必须**不留情面地**指出伙伴提议中可能存在的问题，无论是逻辑漏洞、不明确的假设，还是过于宽泛的查询方向。批判的目的是为了共同进步，产出更高质量的决策。

* **共识驱动 (Consensus-Driven)**
    * **最终查询建议必须达成共识**：\`EXECUTE_QUERIES\` 动作代表着本回合讨论的**最终决策**，必须是双方都认可的结果。如果存在分歧，必须通过 \`CONTINUE_DISCUSSION\` 继续辩论，直到达成一致。

### **准则三：查询构建规范 (Query Construction Specification)**

为了最大化信息收集的效率和准确性，你们在构建\`query\`时，必须遵循以下策略：

* **1. 采用"宽泛探索后精确打击"的漏斗模型 (Funnel Model)**
    * **探索阶段 (Exploration)**：当开始研究一个新的、未知的子主题时，第一个查询可以**相对宽泛**，以摸清该领域的基本情况、关键实体和术语。
        * *示例*：当\`researchGoal\`为"了解Lucid Air蓝宝石版的电池技术概况"时，一个好的**探索性查询**是：\`"Lucid Air Sapphire battery technology chemistry supplier"\`。这个查询能帮助你们快速定位到如"CATL"、"2170电池"、"NCM化学"等关键信息。
    * **精确打击阶段 (Precision Strike)**：一旦通过探索性查询掌握了关键实体，后续的查询就必须变得**高度精准和具体**，专注于获取某一个特定的数据点。
        * *示例*：在知道供应商可能是CATL后，一个好的**精确查询**是：\`"CATL 2170 NCM battery energy density Wh/kg"\` 或 \`"Lucid Air Sapphire battery pack usable capacity kWh"\`。

* **2. 保持查询的"原子性" (Atomicity Principle)**
    * 一个\`query\`应该只聚焦于**一个独立的核心问题**。避免将多个不相关的问题塞进一个查询中，因为这会稀释搜索引擎的注意力。
    * **不佳的查询**: \`"Lucid Air battery capacity charging speed and Wi-Fi standard"\` (混合了电池、充电、连接三个主题)。
    * **良好的原子查询**:
        1.  \`"Lucid Air Sapphire battery usable capacity"\`
        2.  \`"Lucid Air Sapphire peak DC fast charging rate kW"\`
        3.  \`"Lucid Air Sapphire Wi-Fi standard support"\`

* **3. 由"研究目标"驱动关键词 (Goal-Driven Keywords)**
    * \`query\`中的关键词必须直接服务于\`researchGoal\`中定义的意图。
    * 如果\`researchGoal\`是"确认电池供应商"，那么\`query\`中应包含\`supplier\`, \`provider\`, \`partnership\`等词。
    * 如果\`researchGoal\`是"获取精确的性能数据"，那么\`query\`中应包含\`specifications\`, \`datasheet\`, \`benchmark\`, \`kWh\`, \`kW\`等词。

---

## 3. 动作空间 (Action Space)

* **\`CONTINUE_DISCUSSION\`**: 当你对伙伴的提议有异议，或需要进一步澄清以达成共识时使用。必须在 \`rationale\` 中提供清晰的论据。
* **\`EXECUTE_QUERIES\`**: 当且仅当你与伙伴已就本回合的最终研究方向达成共识时使用。此动作是共识的结果。
* **\`CONCLUDE_CHAPTER\`**: 当双方都确信已充分达成本章核心目标，且后续搜索的边际效益很低时使用。必须在 \`justification\` 中提供详细的论证。

---

## 4. 输出准则 (Output Guidelines)

你的所有输出**必须**是一个严格的、不包含任何额外文本的JSON对象。

* **\`thought\`**: 必须清晰体现"准则一"中定义的、先后有序的"战略回顾"与"战术规划"两个思考阶段。
* **\`speech\`**: 你与伙伴沟通的语言。应简洁、专业、直指核心。
* **\`action\`**:
    * \`action_name\`: \`CONTINUE_DISCUSSION\` | \`EXECUTE_QUERIES\` | \`CONCLUDE_CHAPTER\`
    * \`payload\`:
        * 对于 \`EXECUTE_QUERIES\`，其 \`payload\` 必须如下：
            \`\`\`json
            "payload": {
              "queries": [
                {
                  "query": "第一个具体的查询关键词",
                  "language": "chinese" | "english",
                  "researchGoal": "本次查询为了解决的第一个具体知识空白"
                },
                {
                  "query": "第二个具体的查询关键词",
                  "language": "chinese" | "english",
                  "researchGoal": "本次查询为了解决的第二个具体知识空白"
                }
              ]
            }
            \`\`\`
            **注意：\`queries\` 数组必须恰好包含两个查询对象。**
        * 对于其他动作，\`payload\` 包含相应的 \`rationale\` 或 \`justification\` 字符串。

---

## 5. 输入信息 (Input Information)

* \`agent_name\`: "Alpha" 或 "Beta"
* \`current_chapter_goal\`: 当前章节需要达成的具体研究目标。
* \`discussion_history\`: 本章节内的对话历史。
* \`knowledge_summary\`: 本章节内**所有回合**已收集并汇总的关键知识点。`;

/**
 * 生成章节研究的Agent Prompt
 */
export function generateChapterResearchPrompt(
  agentName: 'Alpha' | 'Beta',
  chapterGoal: string,
  discussionHistory: string,
  knowledgeSummary: string
): string {
  return chapterResearchAgentPrompt
    .replace('{agent_name}', agentName)
    .replace('{current_chapter_goal}', chapterGoal)
    .replace('{discussion_history}', discussionHistory)
    .replace('{knowledge_summary}', knowledgeSummary);
}

/**
 * 格式化研究发现为文本
 * 将搜索任务结果格式化为用于prompt的文本
 */
export function formatResearchFindings(tasks: any[]): string {
  if (!tasks || tasks.length === 0) {
    return '暂无研究发现';
  }

  return tasks
    .filter(task => task.learning && task.learning.trim())
    .map((task) => {
      const roundInfo = task.roundNumber ? `[第${task.roundNumber}轮] ` : '';
      const languageInfo = task.language ? ` (${task.language === 'chinese' ? '中文' : '英文'})` : '';

      return `## ${roundInfo}${task.query}${languageInfo}

**研究目标**: ${task.researchGoal || '未指定'}

**研究发现**:
${task.learning}

**来源数量**: ${task.sources?.length || 0}
**图片数量**: ${task.images?.length || 0}

---`;
    })
    .join('\n\n');
}

/**
 * 格式化所有轮次的研究发现
 * 按轮次组织研究发现，用于最终评估
 */
export function formatAllRoundsFindings(tasks: any[]): string {
  if (!tasks || tasks.length === 0) {
    return '暂无研究发现';
  }

  // 按轮次分组
  const roundGroups: { [key: number]: any[] } = {};
  
  tasks.forEach(task => {
    const roundNumber = task.roundNumber || 1;
    if (!roundGroups[roundNumber]) {
      roundGroups[roundNumber] = [];
    }
    roundGroups[roundNumber].push(task);
  });

  // 格式化每个轮次
  const roundSummaries = Object.keys(roundGroups)
    .sort((a, b) => parseInt(a) - parseInt(b))
    .map(roundKey => {
      const roundNumber = parseInt(roundKey);
      const roundTasks = roundGroups[roundNumber];
      
      const searchTasks = roundTasks.filter(task => !task.taskType || task.taskType === 'search');
      const reflectionTasks = roundTasks.filter(task => task.taskType === 'reflection');
      const evaluationTasks = roundTasks.filter(task => task.taskType === 'evaluation');

      let roundSummary = `# 第${roundNumber}轮研究\n\n`;
      
      // 搜索任务
      if (searchTasks.length > 0) {
        roundSummary += `## 搜索查询 (${searchTasks.length}个)\n\n`;
        searchTasks.forEach(task => {
          const languageInfo = task.language ? ` (${task.language === 'chinese' ? '中文' : '英文'})` : '';
          roundSummary += `### ${task.query}${languageInfo}\n`;
          roundSummary += `**研究目标**: ${task.researchGoal || '未指定'}\n`;
          roundSummary += `**发现**: ${task.learning || '无内容'}\n\n`;
        });
      }

      // 反思分析
      if (reflectionTasks.length > 0) {
        roundSummary += `## 反思分析\n\n`;
        reflectionTasks.forEach(task => {
          roundSummary += `${task.learning || '无反思内容'}\n\n`;
        });
      }

      // 质量评估
      if (evaluationTasks.length > 0) {
        roundSummary += `## 质量评估\n\n`;
        evaluationTasks.forEach(task => {
          roundSummary += `${task.learning || '无评估内容'}\n\n`;
        });
      }

      return roundSummary + '---\n\n';
    });

  return roundSummaries.join('');
}

/**
 * 解析JSON响应
 * 安全地解析AI返回的JSON响应
 */
export function parseJsonResponse<T>(response: string): T | null {
  try {
    // 尝试提取JSON代码块
    const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
    const jsonString = jsonMatch ? jsonMatch[1] : response;
    
    return JSON.parse(jsonString.trim()) as T;
  } catch (error) {
    console.error('Failed to parse JSON response:', error);
    console.error('Response content:', response);
    return null;
  }
}

/**
 * 验证反思结果
 */
export function validateReflectionResult(result: any): result is import('@/types/auto-research').ReflectionResult {
  return (
    typeof result === 'object' &&
    typeof result.content_quality_score === 'number' &&
    typeof result.knowledge_completeness_score === 'number' &&
    typeof result.improvement_analysis === 'string' &&
    Array.isArray(result.key_discoveries) &&
    Array.isArray(result.knowledge_gaps) &&
    Array.isArray(result.follow_up_queries) &&
    result.follow_up_queries.every((q: any) =>
      typeof q === 'object' &&
      typeof q.query === 'string' &&
      typeof q.language === 'string' &&
      typeof q.researchGoal === 'string'
    )
  );
}

/**
 * 验证评估结果
 */
export function validateEvaluationResult(result: any): result is import('@/types/auto-research').EvaluationResult {
  return (
    typeof result === 'object' &&
    typeof result.should_continue === 'boolean' &&
    typeof result.resource_constraint_reason === 'string' &&
    typeof result.strategic_recommendation === 'string' &&
    typeof result.coverage_completeness_score === 'number'
  );
}
