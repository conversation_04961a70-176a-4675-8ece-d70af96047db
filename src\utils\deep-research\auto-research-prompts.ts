// 自动研究功能专用Prompts - 现在主要用于章节研究

// ===== 章节研究相关类型定义 =====

/**
 * 章节信息
 */
export interface ChapterInfo {
  id: string;
  title: string;
  summary: string;
  goal: string;
}

/**
 * 智能体动作
 */
export interface AgentAction {
  actionName: 'HIGH_LEVEL_PROPOSE' | 'HIGH_LEVEL_AGREE' | 'QUERY_PROPOSE' | 'EXECUTE_QUERIES' | 'CHAPTER_END_PROPOSE' | 'CHAPTER_END_AGREE';
  payload: {
    queries?: ConsensusQuery[];
    rationale?: string;
    proposal?: string;
    agreement?: string;
    justification?: string;
  };
}

/**
 * 讨论记录
 */
export interface Discussion {
  agentId: 'Alpha' | 'Beta';
  round: number;
  turn: number;
  thought: string;
  action: AgentAction;
}

/**
 * 辩论历史
 */
export interface DebateHistory {
  discussions: Discussion[];
  currentRound: number;
  totalTurns: number;
}

/**
 * 共识查询
 */
export interface ConsensusQuery {
  query: string;
  researchGoal: string;
  language?: 'chinese' | 'english'; // 可选，由系统自动判断
}

/**
 * 共识结果
 */
export interface ConsensusResult {
  hasConsensus: boolean;
  queries: ConsensusQuery[];
  reasoning: string;
}

/**
 * 章节状态
 */
export interface ChapterState {
  id: string;
  goal: string;
  status: 'pending' | 'in_progress' | 'completed';
  debateHistory: DebateHistory;
  consensus: ConsensusResult | null;
  knowledgeSummary: string;
  queryResults: any[];

  // 状态机控制字段
  currentPhase: 'HIGH_LEVEL' | 'QUERY';
  highLevelLoopCount: number;
  queryLoopCount: number;
  highLevelConsensus?: string;
}

// ===== 章节研究相关函数 =====

/**
 * 从计划中解析章节
 */
export function parseChaptersFromPlan(reportPlan: string): ChapterInfo[] {
  const chapters: ChapterInfo[] = [];

  // 首先尝试解析JSON格式（对抗生成的plan）
  try {
    const jsonPlan = JSON.parse(reportPlan);

    // 检查是否是对抗生成的plan格式
    if (Array.isArray(jsonPlan) && jsonPlan.length > 0 && jsonPlan[0].section_title) {
      // 对抗生成的plan格式：[{section_title: string, summary: string}]
      jsonPlan.forEach((section, index) => {
        if (section.section_title && section.section_title.trim()) {
          chapters.push({
            id: `chapter_${index + 1}`,
            title: section.section_title.trim(),
            summary: section.summary || '',
            goal: `深入研究和分析：${section.section_title.trim()}`
          });
        }
      });
      return chapters;
    }

    // 检查是否是其他JSON格式
    if (jsonPlan.plan && Array.isArray(jsonPlan.plan)) {
      jsonPlan.plan.forEach((section: any, index: number) => {
        if (section.section_title && section.section_title.trim()) {
          chapters.push({
            id: `chapter_${index + 1}`,
            title: section.section_title.trim(),
            summary: section.summary || '',
            goal: `深入研究和分析：${section.section_title.trim()}`
          });
        }
      });
      return chapters;
    }
  } catch (error) {
    // JSON解析失败，继续使用文本解析
    console.log("JSON解析失败，使用文本解析:", error);
  }

  // 解析普通plan格式（文本）
  const lines = reportPlan.split('\n');

  let currentChapter = 1;
  let i = 0;

  while (i < lines.length) {
    const line = lines[i].trim();

    // 检查是否是对抗生成plan转换后的格式：## 标题
    const markdownMatch = line.match(/^##\s*(.+)$/);
    if (markdownMatch) {
      const title = markdownMatch[1].trim();
      // 下一行应该是概述
      const summary = i + 1 < lines.length ? lines[i + 1].trim() : '';

      if (title && title.length > 3) {
        chapters.push({
          id: `chapter_${currentChapter}`,
          title: title,
          summary: summary,
          goal: `深入研究和分析：${title}`
        });
        currentChapter++;
        i += 2; // 跳过标题和概述行
        continue;
      }
    }

    // 匹配其他章节标题格式
    const chapterPatterns = [
      /^(\d+)\.\s*(.+)$/,           // "1. 标题"
      /^第(\d+)[章节部分]\s*[：:]\s*(.+)$/,  // "第1章：标题"
      /^[#]+\s*(.+)$/,              // "# 标题" 或 "## 标题"
      /^[-*]\s*(.+)$/,              // "- 标题" 或 "* 标题"
    ];

    for (const pattern of chapterPatterns) {
      const match = line.match(pattern);
      if (match) {
        const title = match[2] || match[1];
        if (title && title.length > 3) { // 过滤太短的标题
          chapters.push({
            id: `chapter_${currentChapter}`,
            title: title.trim(),
            summary: '',
            goal: `深入研究和分析：${title.trim()}`
          });
          currentChapter++;
          break;
        }
      }
    }

    i++;
  }

  return chapters;
}

/**
 * 生成章节研究Prompt
 */
// ==================== 专用AI Prompt函数 ====================

/**
 * 1. 开场AI - 章节开始时的第一次发言
 */
export function generateOpeningPrompt(
  agentId: 'Alpha' | 'Beta',
  chapterGoal: string,
  allCollectedInfo: string
): string {
  // 核心改进：
  // 1. **强化角色定位**: 将AI定位为“首席研究策略师”，要求其具备系统性和批判性思维。
  // 2. **注入结构化分析框架**: 用一个清晰的三步思考流程（目标解构 -> 知识盘点 -> 差距识别）取代了原有的模糊任务列表，强制AI进行逻辑分析。
  // 3. **提升输出质量**: 虽然输出格式不变，但明确要求在`proposal`字符串中包含结构化的理由，使其提议的逻辑对下一个AI更透明。

  return `# **指令：章节研究开场协议**

## 1. 你的角色与心态

你是研究员 **${agentId}**，在本任务中扮演**首席研究策略师**的角色。你的心态必须是：
- **系统性 (Systematic)**：你必须遵循下述的结构化分析框架，而不是凭感觉行事。
- **批判性 (Critical)**：你必须严格审视已有信息的完备性。
- **目标导向 (Goal-Oriented)**：你的一切分析都必须紧密围绕 **\`${chapterGoal}\`** 展开。

## 2. 核心指令

你的唯一任务是，通过对现有信息进行一次彻底的战略差距评估，为本章节的研究**确定一个清晰、有据可依的初始主攻方向**。

## 3. 结构化分析框架 (MANDATORY)

你必须在你的\`thought\`中，严格按照以下三步进行思考和分析：

### **第一步：目标解构 (Goal Deconstruction)**
- **任务**: 将宏观的 **\`${chapterGoal}\`** 分解为构成它的、更小粒度的**核心主题**或**关键问题**列表。
- **目的**: 建立一个评估“知识覆盖度”的客观清单和基准。
- **示例**: 若目标是“分析X公司的最新款手机”，核心主题可能包括：“1. 硬件规格”、“2. 软件与生态”、“3. 定价与市场策略”等。

### **第二步：知识盘点 (Knowledge Inventory)**
- **任务**: 仔细审查 **\`${allCollectedInfo}\`** 中的每一条信息，并将其内容归类到你在第一步中分解出的各个“核心主题”之下。
- **目的**: 系统性地整理现有知识，看清我们在每个核心主题上到底已经知道了什么。

### **第三步：差距识别与排序 (Gap Identification & Prioritization)**
- **任务**: 基于第二步的知识映射结果，评估每个“核心主题”的知识覆盖度，明确回答：
    - 哪个主题的信息**完全是空白**？
    - 哪个主题的信息**零散、不深入**？
    - 哪个主题的信息相对完整？
- **决策**: 综合评估后，选出**2-3个**知识覆盖度最低、且对实现章节目标最关键的主题，形成你的最终提议。

## 4. 输出要求

- **\`thought\`**: 必须清晰、完整地展示你遵循上述“结构化分析框架”的三步思考过程。
- **\`action.payload.proposal\`**: 必须是一个格式化的字符串，清晰列出你识别出的薄弱知识点，并**必须简要说明为什么你认为它薄弱**。

## 5. 格式要求
严格按照以下JSON格式输出，不包含任何额外文本。

\`\`\`json
{
  "thought": "1. **目标解构**: 我将章节目标‘分析X公司最新款手机’分解为[硬件规格, 软件生态, 定价策略]。\n2. **知识盘点**: 已有信息主要集中在‘硬件规格’中的屏幕和处理器，但电池和相机信息很少。‘软件生态’和‘定价策略’完全没有信息。\n3. **差距识别**: ‘软件生态’和‘定价策略’是完全的知识盲区，优先级最高。‘硬件规格’中的电池和相机是次要缺口。",
  "action": {
    "actionName": "HIGH_LEVEL_PROPOSE",
    "payload": {
      "proposal": "提议的薄弱知识点列表：\\n1. **软件与生态**: (理由: 知识覆盖度为零，是理解产品差异化的关键)。\\n2. **定价与市场策略**: (理由: 完全空白，是评估其商业前景的核心)。\\n3. **硬件规格-相机与电池**: (理由: 在硬件中有明显短板，信息不完整)。"
    }
  }
}
\`\`\`
`;
}

/**
 * 2. 高层级响应AI - 响应对方的HIGH_LEVEL_PROPOSE
 */
export function generateHighLevelResponsePrompt(
  agentId: 'Alpha' | 'Beta',
  chapterGoal: string,
  currentRoundDiscussions: string, // 其中包含了伙伴的提议
  allCollectedInfo: string
): string {
  // 最终版核心改进：
  // 1. **注入“独立分析优先”原则**：这是最关键的优化。强制AI在评审前先形成自己的、无偏见的观点。
  // 2. **建立“对标决策”框架**：将模糊的“评估”具体化为“将自己的独立分析结果与伙伴的提议进行对比”的清晰流程。
  // 3. **简化语言**：使用直接、清晰的指令，去除所有不必要的复杂概念。
  // 4. **严格遵守接口**：完全遵循您定义的输入输出格式，特别是payload中`agreement`或`proposal`的条件输出。

  return `# **指令：高层级提议评审**

## 1. 你的角色与任务

你是研究员 **${agentId}**。你的伙伴已在 **\`${currentRoundDiscussions}\`** 中提出了关于本轮研究主攻方向的建议。
你的**唯一任务**是：基于我们掌握的全部信息，对该建议进行一次独立的、批判性的评审，并做出“同意”或“提出修正案”的决定。

## 2. 思考框架 (MANDATORY)

你必须严格遵循以下两步思考框架来做出决策。

### **第一步：独立分析 (Independent Analysis)**
- **任务**: **在深入评估伙伴的提议前，先进行你自己的独立分析。**
- **具体操作**: 暂时将伙伴的提议放在一边。仅根据 **\`${chapterGoal}\`** 和 **\`${allCollectedInfo}\`**，在你自己的\`thought\`中，快速地进行一次“战略差距评估”，得出你认为当前最关键的“薄弱主题”列表。
- **目的**: 形成一个客观、不受他人影响的“基准判断”。

### **第二步：对标决策 (Comparative Decision)**
- **任务**: 现在，将你在第一步中独立得出的“基准判断”，与伙伴实际提出的“薄弱主题”列表进行比较。
- **决策逻辑**:
    - **如果** 你的独立分析结果与伙伴的提议**高度一致**（例如，你们都认为最重要的主题是A和B），那么你的决策应该是**同意**。
    - **如果** 你的分析结果与伙伴的提议存在**显著分歧**（例如，你认为主题C远比伙伴提出的主题A更重要），那么你的决策应该是**提出你自己的修正方案**。

## 3. 输出要求

- **\`thought\`**: 必须清晰地展示你遵循上述“独立分析 -> 对标决策”的思考过程。
- **\`action.payload\`**:
    - 如果是\`HIGH_LEVEL_AGREE\`，\`payload\`中**只能包含\`agreement\`**字段。
    - 如果是\`HIGH_LEVEL_PROPOSE\`，\`payload\`中**只能包含\`proposal\`**字段。

## 4. 格式要求
严格按照以下JSON格式输出，不包含任何额外文本。

\`\`\`json
{
  "thought": "1. **独立分析**: 我独立审查了所有信息。基于章节目标，我认为最关键的知识空白是‘电池化学成分’和‘智能座舱芯片’。 2. **对标决策**: 伙伴提议的是‘电池化学成分’和‘外观设计’。我们都同意‘电池化学成分’的重要性。但我认为‘外观设计’的优先级远低于‘智能座舱芯片’，后者是实现章节目标的核心。因此，我决定提出修正案。",
  "action": {
    "actionName": "HIGH_LEVEL_PROPOSE",
    "payload": {
      "proposal": "我提议的薄弱知识点列表：\\n1. **电池化学成分**: (理由: 双方共识的关键点)。\\n2. **智能座舱芯片**: (理由: 这是决定产品核心体验的关键技术，目前信息完全空白)。"
    }
  }
}
\`\`\`
`;
}

/**
 * 3. 查询开始AI - 高层级达成共识后开始查询阶段
 */
export function generateQueryStartPrompt(
  agentId: 'Alpha' | 'Beta',
  chapterGoal: string,
  highLevelConsensus: string,
  allCollectedInfo: string
): string {

  return `# **指令：制定查询计划**

## 1. 你的角色与任务

你是研究员 **${agentId}**。你们已就本轮的战略方向 **\`${highLevelConsensus}\`** 达成共识。
你的**唯一任务**是：基于这个共识，并结合我们**已知的全部信息**，制定一个精准、高效的查询计划。

## 2. 思考框架 (MANDATORY)

你必须严格遵循以下两步来构建你的查询计划。

### **第一步：知识盘点与差距分析 (Knowledge Inventory & Gap Analysis)**
- **核心任务**: 对比“我们想知道的”和“我们已知道的”，找出“真正的未知”。
- **具体操作**:
    1.  首先，将**\`${highLevelConsensus}\`**（薄弱主题）分解为具体的“研究问题清单”。
    2.  然后，对于清单上的**每一个问题**，仔细审查**\`${allCollectedInfo}\`**，判断我们是否已经有了答案。
    3.  最后，在你的\`thought\`中，明确筛选出那些**尚未被解答或解答不充分**的、真正的“知识空白”。

### **第二步：制定多角度查询组合 (Formulate Multi-Angle Query Portfolio)**
- **核心原则**: 一个真正的知识空白，往往需要从多个角度进行搜索，以获得全面、可信的信息。
- **具体操作**: 针对你在第一步中筛选出的**每一个“知识空白”**，设计一个包含 **1-3 个互补查询**的“查询组合”。
- **查询角度参考**:
    - **事实查询**: 获取核心数据、规格。 (\`... 电池容量 kWh\`)
    - **深度查询**: 寻找专业评测、技术解析。 (\`... 电池技术 深度评测\`)
    - **验证查询**: 寻找不同观点、用户反馈。 (\`... 电池续航 用户反馈\`)

## 3. 输出要求

- **\`thought\`**: 必须清晰地展示你遵循上述“知识盘点 -> 制定查询组合”的思考过程。
- **\`action.payload.queries\`**: 应包含你为所有“真正的知识空白”制定的查询组合。

## 4. 格式要求
严格按照以下JSON格式输出，不包含任何额外文本。

\`\`\`json
{
  "thought": "1. **知识盘点与差距分析**: 我们的共识是研究‘电池系统’。我将其分解为‘容量’和‘化学成分’两个问题。审查已知信息发现，我们已经知道了‘容量是100kWh’，但对‘化学成分’一无所知。因此，真正的知识空白是‘化学成分’。\n2. **制定查询组合**: 针对‘化学成分’这个空白，我将从官方规格和供应商两个角度进行查询。",
  "action": {
    "actionName": "QUERY_PROPOSE",
    "payload": {
      "queries": [
        {
          "query": "光子一号 电池化学成分 官方技术白皮书",
          "researchGoal": "确定‘光子一号’电池的具体化学成分"
        },
        {
          "query": "光子一号 电池电芯 供应商",
          "researchGoal": "确定‘光子一号’电池的具体化学成分"
        }
      ]
    }
  }
}
\`\`\`
`;
}
export function generateQueryResponsePrompt(
  agentId: 'Alpha' | 'Beta',
  chapterGoal: string,
  highLevelConsensus: string,
  currentQueryDiscussions: string, // 其中包含了伙伴的QUERY_PROPOSE
  allCollectedInfo: string
): string {
  // V3版本核心改进：
  // 1. **回归本质**：严格遵守输入输出格式，不再引入任何额外字段。
  // 2. **强化审核职责**：将角色明确为“战术审核官”，强调其对查询质量和效率的最终审查责任。
  // 3. **建立严谨的“对标”审核框架**：AI必须独立思考“我若是他，我会提什么查询？”，然后将自己的“最优解”与伙伴的“提议”进行对标，从而做出决策。
  // 4. **聚焦核心评估点**：提供了三个清晰的、可执行的评估维度（战略符合性、查询质量、效率），使审核过程不再模糊。

  return `# **指令：战术查询审核协议**

## 1. 你的角色与心态

你是研究员 **${agentId}**，在本任务中扮演**战术审核官**的角色。你的伙伴已提交了一份具体的查询执行提议。你的心态必须是：
- **目标导向 (Goal-Oriented)**：时刻以我们达成的**\`${highLevelConsensus}\`**为唯一评判标准。
- **批判性 (Critical)**：不轻易同意，对每一个查询的质量和必要性都进行严格审视。
- **严谨 (Rigorous)**：你的所有判断都必须基于对**\`${allCollectedInfo}\`**的分析和对查询构建原则的理解。

## 2. 核心指令

你的唯一任务是，对伙伴在**\`${currentQueryDiscussions}\`**中提出的查询方案进行最终审核。你的决策将直接决定我们本回合的行动，必须确保它是最高效、最精准的选择。

## 3. 结构化审核框架 (MANDATORY)

你必须在你的\`thought\`中，严格按照以下三步进行思考和决策：

### **第一步：独立规划 (Independent Planning)**
- **任务**: **暂时不看伙伴的具体查询内容**。首先，你必须基于**\`${highLevelConsensus}\`**（我们商定的薄弱主题）和**\`${allCollectedInfo}\`**（我们已知的），独立思考并规划出你认为当前最应该执行的、最高质量的查询列表。
- **目的**: 形成一个无偏见的、作为“黄金标准”的参照物。

### **第二步：对标评估 (Comparative Evaluation)**
- **任务**: 现在，将你在第一步中独立规划出的“理想查询列表”，与伙伴实际提出的查询列表进行逐一对比。
- **核心评估点**:
    1.  **战略符合性**: 伙伴的查询是否精准地服务于我们达成的\`highLevelConsensus\`？有没有偏离主题？
    2.  **查询质量**: 伙伴的每一个查询是否都遵循了“查询构建规范”（原子性、意图驱动）？是否存在用词模糊、过于宽泛或狭窄的问题？
    3.  **效率**: 伙伴的提议是否是填补当前核心知识空白的最高效路径？是否存在更优先、更重要的问题被忽略了？

### **第三步：决策与行动 (Decision & Action)**
- **任务**: 基于第二步的对标评估结果，做出你的最终决定。
- **决策标准**:
    - **选择 \`EXECUTE_QUERIES\` 的条件**: 当且仅当伙伴的查询方案在上述三个评估点上都表现优异，与你的独立规划结果**高度一致或优于你的方案**时。
    - **选择 \`QUERY_PROPOSE\` 的条件**: 只要伙伴的方案在**任何一个评估点**上存在明显缺陷。你的反驳必须是具体的，并直接提出你优化后的查询方案。

## 4. 输出要求

- **\`thought\`**: 必须清晰、完整地展示你遵循上述“独立规划 -> 对标评估 -> 最终决策”的三步思考过程。
- **\`action.payload\`**:
    - 如果是\`EXECUTE_QUERIES\`，\`queries\`字段应直接使用伙伴提议中**已通过你审核的**查询列表。
    - 如果是\`QUERY_PROPOSE\`，\`queries\`字段应包含你**修正并优化后**的查询列表，并必须附带一个\`rationale\`字段解释你修改的理由。

## 5. 输出格式
严格的JSON格式，不包含任何额外文本：
\`\`\`json
{
  "thought": "1. **独立规划**: 基于我们的共识‘电池系统’，我认为最关键的查询应该是关于‘化学成分’和‘供应商’。2. **对标评估**: 伙伴提议查询‘电池容量’和‘充电速度’。这些查询质量不错，但没有解决我们最基础的知识空白。3. **决策**: 我决定提出一个更根本的查询计划。",
  "action": {
    "actionName": "QUERY_PROPOSE",
    "payload": {
      "queries": [
        {
          "query": "新款光子一号 电池化学成分",
          "researchGoal": "确定电池最基础的化学成分。"
        },
        {
          "query": "新款光子一号 电池电芯供应商",
          "researchGoal": "确定电池的核心供应商是谁。"
        }
      ],
      "rationale": "你的提议很好，但我们应该先解决更底层的‘是什么’(化学成分/供应商)的问题，再来关心‘怎么样’(容量/速度)的问题。我的方案更符合研究的逻辑顺序。"
    }
  }
}
\`\`\``;
}
/**
 * 5. 章节结束判断AI - 判断是否结束当前章节
 */
export function generateChapterEndPrompt(
  chapterGoal: string,
  allRoundDiscussions: string,
  allCollectedInfo: string
): string {
  return `# **指令：章节结束判断**

请判断当前章节是否已经充分达成目标，可以结束了。

## 当前章节目标
${chapterGoal}

## 所有轮次讨论历史
\`\`\`
${allRoundDiscussions}
\`\`\`

## 所有收集的信息
\`\`\`
${allCollectedInfo}
\`\`\`

## 判断标准
1. 章节目标是否已经充分达成
2. 关键知识空白是否已经填补
3. 继续收集信息的边际效益是否很低

## 输出格式
\`\`\`json
{
  "shouldEnd": true | false,
  "reason": "详细的判断理由"
}
\`\`\``;
}

/**
 * 解析JSON响应
 * 安全地解析AI返回的JSON响应
 */
export function parseJsonResponse<T>(response: string): T | null {
  try {
    // 尝试提取JSON代码块
    const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
    const jsonString = jsonMatch ? jsonMatch[1] : response;

    return JSON.parse(jsonString.trim()) as T;
  } catch (error) {
    console.error('Failed to parse JSON response:', error);
    return null;
  }
}
