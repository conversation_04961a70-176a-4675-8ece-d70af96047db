// 自动研究功能专用Prompts - 现在主要用于章节研究

// ===== 章节研究相关类型定义 =====

/**
 * 章节信息
 */
export interface ChapterInfo {
  id: string;
  title: string;
  summary: string;
  goal: string;
}

/**
 * 智能体动作
 */
export interface AgentAction {
  actionName: 'HIGH_LEVEL_PROPOSE' | 'HIGH_LEVEL_AGREE' | 'QUERY_PROPOSE' | 'EXECUTE_QUERIES' | 'CHAPTER_END_PROPOSE' | 'CHAPTER_END_AGREE';
  payload: {
    queries?: ConsensusQuery[];
    rationale?: string;
    proposal?: string;
    agreement?: string;
    justification?: string;
  };
}

/**
 * 讨论记录
 */
export interface Discussion {
  agentId: 'Alpha' | 'Beta';
  round: number;
  turn: number;
  thought: string;
  action: AgentAction;
}

/**
 * 辩论历史
 */
export interface DebateHistory {
  discussions: Discussion[];
  currentRound: number;
  totalTurns: number;
}

/**
 * 共识查询
 */
export interface ConsensusQuery {
  query: string;
  researchGoal: string;
  language?: 'chinese' | 'english'; // 可选，由系统自动判断
}

/**
 * 共识结果
 */
export interface ConsensusResult {
  hasConsensus: boolean;
  queries: ConsensusQuery[];
  reasoning: string;
}

/**
 * 章节状态
 */
export interface ChapterState {
  id: string;
  goal: string;
  status: 'pending' | 'in_progress' | 'completed';
  debateHistory: DebateHistory;
  consensus: ConsensusResult | null;
  knowledgeSummary: string;
  queryResults: any[];

  // 状态机控制字段
  currentPhase: 'HIGH_LEVEL' | 'QUERY';
  highLevelLoopCount: number;
  queryLoopCount: number;
  highLevelConsensus?: string;
}

// ===== 章节研究相关函数 =====

/**
 * 从计划中解析章节
 */
export function parseChaptersFromPlan(reportPlan: string): ChapterInfo[] {
  const chapters: ChapterInfo[] = [];

  // 首先尝试解析JSON格式（对抗生成的plan）
  try {
    const jsonPlan = JSON.parse(reportPlan);

    // 检查是否是对抗生成的plan格式
    if (Array.isArray(jsonPlan) && jsonPlan.length > 0 && jsonPlan[0].section_title) {
      // 对抗生成的plan格式：[{section_title: string, summary: string}]
      jsonPlan.forEach((section, index) => {
        if (section.section_title && section.section_title.trim()) {
          chapters.push({
            id: `chapter_${index + 1}`,
            title: section.section_title.trim(),
            summary: section.summary || '',
            goal: `深入研究和分析：${section.section_title.trim()}`
          });
        }
      });
      return chapters;
    }

    // 检查是否是其他JSON格式
    if (jsonPlan.plan && Array.isArray(jsonPlan.plan)) {
      jsonPlan.plan.forEach((section: any, index: number) => {
        if (section.section_title && section.section_title.trim()) {
          chapters.push({
            id: `chapter_${index + 1}`,
            title: section.section_title.trim(),
            summary: section.summary || '',
            goal: `深入研究和分析：${section.section_title.trim()}`
          });
        }
      });
      return chapters;
    }
  } catch (error) {
    // JSON解析失败，继续使用文本解析
    console.log("JSON解析失败，使用文本解析:", error);
  }

  // 解析普通plan格式（文本）
  const lines = reportPlan.split('\n');

  let currentChapter = 1;
  let i = 0;

  while (i < lines.length) {
    const line = lines[i].trim();

    // 检查是否是对抗生成plan转换后的格式：## 标题
    const markdownMatch = line.match(/^##\s*(.+)$/);
    if (markdownMatch) {
      const title = markdownMatch[1].trim();
      // 下一行应该是概述
      const summary = i + 1 < lines.length ? lines[i + 1].trim() : '';

      if (title && title.length > 3) {
        chapters.push({
          id: `chapter_${currentChapter}`,
          title: title,
          summary: summary,
          goal: `深入研究和分析：${title}`
        });
        currentChapter++;
        i += 2; // 跳过标题和概述行
        continue;
      }
    }

    // 匹配其他章节标题格式
    const chapterPatterns = [
      /^(\d+)\.\s*(.+)$/,           // "1. 标题"
      /^第(\d+)[章节部分]\s*[：:]\s*(.+)$/,  // "第1章：标题"
      /^[#]+\s*(.+)$/,              // "# 标题" 或 "## 标题"
      /^[-*]\s*(.+)$/,              // "- 标题" 或 "* 标题"
    ];

    for (const pattern of chapterPatterns) {
      const match = line.match(pattern);
      if (match) {
        const title = match[2] || match[1];
        if (title && title.length > 3) { // 过滤太短的标题
          chapters.push({
            id: `chapter_${currentChapter}`,
            title: title.trim(),
            summary: '',
            goal: `深入研究和分析：${title.trim()}`
          });
          currentChapter++;
          break;
        }
      }
    }

    i++;
  }

  return chapters;
}

/**
 * 生成章节研究Prompt
 */
// ==================== 专用AI Prompt函数 ====================

/**
 * 1. 开场AI - 章节开始时的第一次发言
 */
export function generateOpeningPrompt(
  agentId: 'Alpha' | 'Beta',
  chapterGoal: string,
  allCollectedInfo: string
): string {
  return `# **指令：章节研究开场**

你是研究员 ${agentId}，正在开始一个新的章节研究。你的任务是进行第一阶段思考，评估当前知识的薄弱环节，并提出高层级的研究方向。

## 当前章节目标
${chapterGoal}

## 已收集的信息
\`\`\`
${allCollectedInfo}
\`\`\`

## 你的任务
1. **第一阶段思考：战略差距评估**
   - 重申本章节目标
   - 审查已有知识是否服务于目标
   - 识别最薄弱的知识领域（按优先级排序）

2. **输出要求**
   - thought: 第一阶段思考过程
   - action: { actionName: "HIGH_LEVEL_PROPOSE", payload: { proposal: "薄弱知识点列表" } }

## 输出格式
严格的JSON格式，不包含任何额外文本：
\`\`\`json
{
  "thought": "你的第一阶段思考过程",
  "action": {
    "actionName": "HIGH_LEVEL_PROPOSE",
    "payload": {
      "proposal": "基于分析得出的薄弱知识点列表"
    }
  }
}
\`\`\``;
}

/**
 * 2. 高层级响应AI - 响应对方的HIGH_LEVEL_PROPOSE
 */
export function generateHighLevelResponsePrompt(
  agentId: 'Alpha' | 'Beta',
  chapterGoal: string,
  currentRoundDiscussions: string,
  allCollectedInfo: string
): string {
  return `# **指令：高层级响应**

你是研究员 ${agentId}，正在响应对方的高层级提议。你需要评估对方提出的薄弱知识点是否合理。

## 当前章节目标
${chapterGoal}

## 当前轮次讨论历史
\`\`\`
${currentRoundDiscussions}
\`\`\`

## 已收集的信息
\`\`\`
${allCollectedInfo}
\`\`\`

## 你的任务
1. **第一阶段思考：战略差距评估**
   - 重申本章节目标
   - 评估对方提出的薄弱知识点是否准确
   - 判断是否同意对方的评估

2. **输出选择**
   - 如果同意：HIGH_LEVEL_AGREE
   - 如果不同意：HIGH_LEVEL_PROPOSE（提出你的薄弱知识点列表）

## 输出格式
\`\`\`json
{
  "thought": "你的第一阶段思考过程",
  "action": {
    "actionName": "HIGH_LEVEL_AGREE" | "HIGH_LEVEL_PROPOSE",
    "payload": {
      "agreement": "同意的理由" | "proposal": "你的薄弱知识点列表"
    }
  }
}
\`\`\``;
}

/**
 * 3. 查询开始AI - 高层级达成共识后开始查询阶段
 */
export function generateQueryStartPrompt(
  agentId: 'Alpha' | 'Beta',
  chapterGoal: string,
  highLevelConsensus: string,
  allCollectedInfo: string
): string {
  return `# **指令：查询阶段开始**

你是研究员 ${agentId}，高层级已达成共识，现在需要制定具体的查询计划。

## 当前章节目标
${chapterGoal}

## 已达成的高层级共识
\`\`\`
${highLevelConsensus}
\`\`\`

## 已收集的信息
\`\`\`
${allCollectedInfo}
\`\`\`

## 你的任务
1. **第二阶段思考：战术细节规划**
   - 基于高层级共识制定具体查询
   - 在确定的薄弱知识点内进行精细化分析
   - 制定2个具体的查询关键词

2. **输出要求**
   - thought: 第二阶段思考过程
   - action: { actionName: "QUERY_PROPOSE", payload: { queries: [...] } }

## 输出格式
\`\`\`json
{
  "thought": "你的第二阶段思考过程",
  "action": {
    "actionName": "QUERY_PROPOSE",
    "payload": {
      "queries": [
        {
          "query": "第一个具体查询关键词",
          "researchGoal": "解决的具体知识空白"
        },
        {
          "query": "第二个具体查询关键词",
          "researchGoal": "解决的具体知识空白"
        }
      ]
    }
  }
}
\`\`\``;
}

/**
 * 4. 查询响应AI - 响应对方的QUERY_PROPOSE
 */
export function generateQueryResponsePrompt(
  agentId: 'Alpha' | 'Beta',
  chapterGoal: string,
  highLevelConsensus: string,
  currentQueryDiscussions: string,
  allCollectedInfo: string
): string {
  return `# **指令：查询响应**

你是研究员 ${agentId}，正在评估对方提出的查询方案。

## 当前章节目标
${chapterGoal}

## 已达成的高层级共识
\`\`\`
${highLevelConsensus}
\`\`\`

## 当前查询讨论历史
\`\`\`
${currentQueryDiscussions}
\`\`\`

## 已收集的信息
\`\`\`
${allCollectedInfo}
\`\`\`

## 你的任务
1. **第二阶段思考：战术细节规划**
   - 评估对方的查询是否合适
   - 判断是否能有效填补知识空白

2. **输出选择**
   - 如果同意：EXECUTE_QUERIES
   - 如果不同意：QUERY_PROPOSE（提出你的查询方案）

## 输出格式
\`\`\`json
{
  "thought": "你的第二阶段思考过程",
  "action": {
    "actionName": "EXECUTE_QUERIES" | "QUERY_PROPOSE",
    "payload": {
      "queries": [对方的查询或你的查询]
    }
  }
}
\`\`\``;
}

/**
 * 5. 章节结束判断AI - 判断是否结束当前章节
 */
export function generateChapterEndPrompt(
  chapterGoal: string,
  allRoundDiscussions: string,
  allCollectedInfo: string
): string {
  return `# **指令：章节结束判断**

请判断当前章节是否已经充分达成目标，可以结束了。

## 当前章节目标
${chapterGoal}

## 所有轮次讨论历史
\`\`\`
${allRoundDiscussions}
\`\`\`

## 所有收集的信息
\`\`\`
${allCollectedInfo}
\`\`\`

## 判断标准
1. 章节目标是否已经充分达成
2. 关键知识空白是否已经填补
3. 继续收集信息的边际效益是否很低

## 输出格式
\`\`\`json
{
  "shouldEnd": true | false,
  "reason": "详细的判断理由"
}
\`\`\``;
}

/**
 * 解析JSON响应
 * 安全地解析AI返回的JSON响应
 */
export function parseJsonResponse<T>(response: string): T | null {
  try {
    // 尝试提取JSON代码块
    const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
    const jsonString = jsonMatch ? jsonMatch[1] : response;

    return JSON.parse(jsonString.trim()) as T;
  } catch (error) {
    console.error('Failed to parse JSON response:', error);
    return null;
  }
}
