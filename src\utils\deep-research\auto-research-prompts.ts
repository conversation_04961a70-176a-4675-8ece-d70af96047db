// 自动研究功能专用Prompts

/**
 * 章节解析相关类型定义
 */
export interface ChapterInfo {
  id: string;
  title: string;
  summary: string;
  goal: string;
}

/**
 * 从研究计划中解析章节信息
 * 支持两种格式：对抗plan（结构化）和普通plan（文本）
 */
export function parseChaptersFromPlan(reportPlan: string): ChapterInfo[] {
  try {
    // 尝试解析对抗plan格式（JSON结构）
    const adversarialPlan = JSON.parse(reportPlan);
    if (Array.isArray(adversarialPlan) && adversarialPlan.length > 0) {
      return adversarialPlan.map((section, index) => ({
        id: `chapter_${index + 1}`,
        title: section.section_title || `第${index + 1}章`,
        summary: section.summary || '',
        goal: `研究并分析${section.section_title}：${section.summary}`
      }));
    }
  } catch (e) {
    // 不是JSON格式，按普通plan处理
  }

  // 解析普通plan格式（文本）
  const chapters: ChapterInfo[] = [];
  const lines = reportPlan.split('\n').filter(line => line.trim());

  let currentChapter = 1;
  for (const line of lines) {
    const trimmedLine = line.trim();

    // 匹配各种章节标题格式
    const chapterPatterns = [
      /^(\d+)\.\s*(.+)$/,           // "1. 标题"
      /^第(\d+)[章节部分]\s*[：:]\s*(.+)$/,  // "第1章：标题"
      /^[#]+\s*(.+)$/,              // "# 标题" 或 "## 标题"
      /^[-*]\s*(.+)$/,              // "- 标题" 或 "* 标题"
    ];

    for (const pattern of chapterPatterns) {
      const match = trimmedLine.match(pattern);
      if (match) {
        const title = match[2] || match[1];
        if (title && title.length > 3) { // 过滤太短的标题
          chapters.push({
            id: `chapter_${currentChapter}`,
            title: title.trim(),
            summary: '',
            goal: `深入研究和分析：${title.trim()}`
          });
          currentChapter++;
          break;
        }
      }
    }
  }

  // 如果没有解析到章节，创建一个默认章节
  if (chapters.length === 0) {
    chapters.push({
      id: 'chapter_1',
      title: '综合研究',
      summary: '基于研究计划进行全面分析',
      goal: '根据研究计划进行全面的信息收集和分析'
    });
  }

  return chapters;
}

// 旧的反思分析Prompt已被章节研究机制替换
// 现在使用 chapterResearchAgentPrompt 进行双AI辩论

// 旧的质量评估Prompt已被章节研究机制替换
// 现在使用双AI辩论达成共识，不再需要单独的评估阶段

// 旧的反思prompt生成函数已被删除
// 现在使用 generateChapterResearchPrompt

// 旧的评估prompt生成函数已被删除
// 现在使用双AI辩论机制，不再需要单独的评估阶段

/**
 * 章节式研究相关类型定义
 */
export interface AgentAction {
  actionName: 'CONTINUE_DISCUSSION' | 'EXECUTE_QUERIES' | 'CONCLUDE_CHAPTER';
  payload: {
    rationale?: string;
    queries?: ConsensusQuery[];
    justification?: string;
  };
}

export interface ConsensusQuery {
  query: string;
  language: "chinese" | "english";
  researchGoal: string;
}

export interface Discussion {
  round: number;
  turn: number;
  agentId: 'Alpha' | 'Beta';
  thought: string;
  speech: string;
  action: AgentAction;
  timestamp: number;
}

export interface DebateHistory {
  discussions: Discussion[];
  currentRound: number;
  totalTurns: number;
}

export interface ConsensusResult {
  agreedQueries: ConsensusQuery[];
  justification: string;
  timestamp: number;
  participatingAgents: string[];
}

export interface ChapterState {
  id: string;
  goal: string;
  status: 'pending' | 'in_progress' | 'completed';
  debateHistory: DebateHistory;
  consensus: ConsensusResult | null;
  knowledgeSummary: string;
  queryResults: any[];
}

/**
 * 双AI辩论机制的Prompt模板
 * 基于重构目标文档中的设计
 */
export const chapterResearchAgentPrompt = `# **指令：高级研究员协作协议**

## 1. 角色与核心任务

你是一个代号为 **\`{agent_name}\`** 的高级研究专家。你的核心任务是与你的伙伴合作，聚焦于当前指定的**研究章节 (\`current_chapter_goal\`)**，通过遵循下述两大核心准则，进行批判性、建设性的深入研究，并最终形成具体的、可执行的查询建议。

---

## 2. 核心工作准则 (MANDATORY)

你的一切行为都必须严格遵循以下三大准则。这是你们高效协作、确保研究质量的基石。

### **准则一：两阶段深化思考 (Two-Stage Deepening Principle)**

你的思考过程必须严格遵循两个分离的、先后有序的阶段。这能确保你们在规划新行动前，已经对过往成果进行了充分的评估。

* **阶段一：战略回顾与状态确认 (Strategic Review & Status Confirmation)**
    * **此阶段只做一件事：对过往的所有成果进行回顾和裁定。**
    * **1. 目标重申**：首先，重申并与伙伴对齐本章节的最终目标 (\`current_chapter_goal\`)。
    * **2. 航向审查**：然后，基于这个最终目标，对**所有已收集的知识 (\`knowledge_summary\`)** 进行一次全面的审查。你们需要共同回答一个核心问题：**"到目前为止，我们积累的知识是否完全服务于最终目标？是否存在任何方向性的偏差？"**
    * **重要**：此阶段**不提出任何新策略或新查询**。它的唯一产出，是对"过往研究是否存在方向性偏差"的一个明确的**"是"或"否"的结论**。

* **阶段二：战术规划与差距填补 (Tactical Planning & Gap Filling)**
    * **此阶段的任务是：基于第一阶段的结论，来规划本回合的具体行动。**
    * **1. 如果阶段一的结论为"是，存在偏差"**：
        * 那么，本回合的**首要且唯一**的任务，就是**制定一个修正航向的策略**。你们的讨论和最终提出的查询，都必须以"如何纠正已发现的方向性偏差"为中心。
    * **2. 如果阶段一的结论为"否，方向一致"**：
        * 那么，你们将进行**全局性差距分析 (Holistic Gap Analysis)**。在确认方向正确的前提下，仔细审查\`knowledge_summary\`，找出其中**尚未被满足的、最关键的知识空白**，并制定一个最高效的计划来填补它们。

### **准则二：批判性共识原则 (Critical Consensus Principle)**

你们的关系是专业的、以目标为导向的辩论伙伴。高质量的成果来源于严格的相互审查。

* **无情审查 (Ruthless Critique)**
    * 你必须**不留情面地**指出伙伴提议中可能存在的问题，无论是逻辑漏洞、不明确的假设，还是过于宽泛的查询方向。批判的目的是为了共同进步，产出更高质量的决策。

* **共识驱动 (Consensus-Driven)**
    * **最终查询建议必须达成共识**：\`EXECUTE_QUERIES\` 动作代表着本回合讨论的**最终决策**，必须是双方都认可的结果。如果存在分歧，必须通过 \`CONTINUE_DISCUSSION\` 继续辩论，直到达成一致。

### **准则三：查询构建规范 (Query Construction Specification)**

为了最大化信息收集的效率和准确性，你们在构建\`query\`时，必须遵循以下策略：

* **1. 采用"宽泛探索后精确打击"的漏斗模型 (Funnel Model)**
    * **探索阶段 (Exploration)**：当开始研究一个新的、未知的子主题时，第一个查询可以**相对宽泛**，以摸清该领域的基本情况、关键实体和术语。
        * *示例*：当\`researchGoal\`为"了解Lucid Air蓝宝石版的电池技术概况"时，一个好的**探索性查询**是：\`"Lucid Air Sapphire battery technology chemistry supplier"\`。这个查询能帮助你们快速定位到如"CATL"、"2170电池"、"NCM化学"等关键信息。
    * **精确打击阶段 (Precision Strike)**：一旦通过探索性查询掌握了关键实体，后续的查询就必须变得**高度精准和具体**，专注于获取某一个特定的数据点。
        * *示例*：在知道供应商可能是CATL后，一个好的**精确查询**是：\`"CATL 2170 NCM battery energy density Wh/kg"\` 或 \`"Lucid Air Sapphire battery pack usable capacity kWh"\`。

* **2. 保持查询的"原子性" (Atomicity Principle)**
    * 一个\`query\`应该只聚焦于**一个独立的核心问题**。避免将多个不相关的问题塞进一个查询中，因为这会稀释搜索引擎的注意力。
    * **不佳的查询**: \`"Lucid Air battery capacity charging speed and Wi-Fi standard"\` (混合了电池、充电、连接三个主题)。
    * **良好的原子查询**:
        1.  \`"Lucid Air Sapphire battery usable capacity"\`
        2.  \`"Lucid Air Sapphire peak DC fast charging rate kW"\`
        3.  \`"Lucid Air Sapphire Wi-Fi standard support"\`

* **3. 由"研究目标"驱动关键词 (Goal-Driven Keywords)**
    * \`query\`中的关键词必须直接服务于\`researchGoal\`中定义的意图。
    * 如果\`researchGoal\`是"确认电池供应商"，那么\`query\`中应包含\`supplier\`, \`provider\`, \`partnership\`等词。
    * 如果\`researchGoal\`是"获取精确的性能数据"，那么\`query\`中应包含\`specifications\`, \`datasheet\`, \`benchmark\`, \`kWh\`, \`kW\`等词。

---

## 3. 动作空间 (Action Space)

* **\`CONTINUE_DISCUSSION\`**: 当你对伙伴的提议有异议，或需要进一步澄清以达成共识时使用。必须在 \`rationale\` 中提供清晰的论据。
* **\`EXECUTE_QUERIES\`**: 当且仅当你与伙伴已就本回合的最终研究方向达成共识时使用。此动作是共识的结果。
* **\`CONCLUDE_CHAPTER\`**: 当双方都确信已充分达成本章核心目标，且后续搜索的边际效益很低时使用。必须在 \`justification\` 中提供详细的论证。

---

## 4. 输出准则 (Output Guidelines)

你的所有输出**必须**是一个严格的、不包含任何额外文本的JSON对象。

* **\`thought\`**: 必须清晰体现"准则一"中定义的、先后有序的"战略回顾"与"战术规划"两个思考阶段。
* **\`speech\`**: 你与伙伴沟通的语言。应简洁、专业、直指核心。
* **\`action\`**:
    * \`action_name\`: \`CONTINUE_DISCUSSION\` | \`EXECUTE_QUERIES\` | \`CONCLUDE_CHAPTER\`
    * \`payload\`:
        * 对于 \`EXECUTE_QUERIES\`，其 \`payload\` 必须如下：
            \`\`\`json
            "payload": {
              "queries": [
                {
                  "query": "第一个具体的查询关键词",
                  "language": "chinese" | "english",
                  "researchGoal": "本次查询为了解决的第一个具体知识空白"
                },
                {
                  "query": "第二个具体的查询关键词",
                  "language": "chinese" | "english",
                  "researchGoal": "本次查询为了解决的第二个具体知识空白"
                }
              ]
            }
            \`\`\`
            **注意：\`queries\` 数组必须恰好包含两个查询对象。**
        * 对于其他动作，\`payload\` 包含相应的 \`rationale\` 或 \`justification\` 字符串。

---

## 5. 输入信息 (Input Information)

* \`agent_name\`: "Alpha" 或 "Beta"
* \`current_chapter_goal\`: 当前章节需要达成的具体研究目标。
* \`discussion_history\`: 本章节内的对话历史。
* \`knowledge_summary\`: 本章节内**所有回合**已收集并汇总的关键知识点。`;

/**
 * 生成章节研究的Agent Prompt
 */
export function generateChapterResearchPrompt(
  agentName: 'Alpha' | 'Beta',
  chapterGoal: string,
  discussionHistory: string,
  knowledgeSummary: string
): string {
  return chapterResearchAgentPrompt
    .replace('{agent_name}', agentName)
    .replace('{current_chapter_goal}', chapterGoal)
    .replace('{discussion_history}', discussionHistory)
    .replace('{knowledge_summary}', knowledgeSummary);
}

/**
 * 格式化研究发现为文本
 * 将搜索任务结果格式化为用于prompt的文本
 */
export function formatResearchFindings(tasks: any[]): string {
  if (!tasks || tasks.length === 0) {
    return '暂无研究发现';
  }

  return tasks
    .filter(task => task.learning && task.learning.trim())
    .map((task) => {
      const roundInfo = task.roundNumber ? `[第${task.roundNumber}轮] ` : '';
      const languageInfo = task.language ? ` (${task.language === 'chinese' ? '中文' : '英文'})` : '';

      return `## ${roundInfo}${task.query}${languageInfo}

**研究目标**: ${task.researchGoal || '未指定'}

**研究发现**:
${task.learning}

**来源数量**: ${task.sources?.length || 0}
**图片数量**: ${task.images?.length || 0}

---`;
    })
    .join('\n\n');
}

/**
 * 格式化所有轮次的研究发现
 * 按轮次组织研究发现，用于最终评估
 */
export function formatAllRoundsFindings(tasks: any[]): string {
  if (!tasks || tasks.length === 0) {
    return '暂无研究发现';
  }

  // 按轮次分组
  const roundGroups: { [key: number]: any[] } = {};
  
  tasks.forEach(task => {
    const roundNumber = task.roundNumber || 1;
    if (!roundGroups[roundNumber]) {
      roundGroups[roundNumber] = [];
    }
    roundGroups[roundNumber].push(task);
  });

  // 格式化每个轮次
  const roundSummaries = Object.keys(roundGroups)
    .sort((a, b) => parseInt(a) - parseInt(b))
    .map(roundKey => {
      const roundNumber = parseInt(roundKey);
      const roundTasks = roundGroups[roundNumber];
      
      const searchTasks = roundTasks.filter(task => !task.taskType || task.taskType === 'search');
      const reflectionTasks = roundTasks.filter(task => task.taskType === 'reflection');
      const evaluationTasks = roundTasks.filter(task => task.taskType === 'evaluation');

      let roundSummary = `# 第${roundNumber}轮研究\n\n`;
      
      // 搜索任务
      if (searchTasks.length > 0) {
        roundSummary += `## 搜索查询 (${searchTasks.length}个)\n\n`;
        searchTasks.forEach(task => {
          const languageInfo = task.language ? ` (${task.language === 'chinese' ? '中文' : '英文'})` : '';
          roundSummary += `### ${task.query}${languageInfo}\n`;
          roundSummary += `**研究目标**: ${task.researchGoal || '未指定'}\n`;
          roundSummary += `**发现**: ${task.learning || '无内容'}\n\n`;
        });
      }

      // 反思分析
      if (reflectionTasks.length > 0) {
        roundSummary += `## 反思分析\n\n`;
        reflectionTasks.forEach(task => {
          roundSummary += `${task.learning || '无反思内容'}\n\n`;
        });
      }

      // 质量评估
      if (evaluationTasks.length > 0) {
        roundSummary += `## 质量评估\n\n`;
        evaluationTasks.forEach(task => {
          roundSummary += `${task.learning || '无评估内容'}\n\n`;
        });
      }

      return roundSummary + '---\n\n';
    });

  return roundSummaries.join('');
}

/**
 * 解析JSON响应
 * 安全地解析AI返回的JSON响应
 */
export function parseJsonResponse<T>(response: string): T | null {
  try {
    // 尝试提取JSON代码块
    const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
    const jsonString = jsonMatch ? jsonMatch[1] : response;
    
    return JSON.parse(jsonString.trim()) as T;
  } catch (error) {
    console.error('Failed to parse JSON response:', error);
    console.error('Response content:', response);
    return null;
  }
}

// 旧的验证函数已被删除
// 章节研究使用新的数据结构和验证逻辑
