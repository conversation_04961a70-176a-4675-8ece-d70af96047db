// 章节式研究管理器
import { generateText } from "ai";
import {
  ChapterInfo,
  ChapterState,
  Discussion,
  AgentAction,
  ConsensusResult,
  ConsensusQuery,
  DebateHistory,
  generateChapterResearchPrompt,
  parseChaptersFromPlan
} from "./auto-research-prompts";

/**
 * 章节研究管理器
 * 负责管理整个章节式研究流程
 */
export class ChapterResearchManager {
  private chapters: Map<string, ChapterState> = new Map();
  private currentChapterIndex: number = 0;
  private chapterList: ChapterInfo[] = [];

  constructor(
    private reportPlan: string,
    private onMessage: (type: string, data: any) => void,
    private createModelProvider: (model: string) => Promise<any>,
    private getSystemPrompt: () => string,
    private getModel: () => { thinkingModel: string; networkingModel: string }
  ) {
    console.log("🔍 开始解析研究计划:", this.reportPlan);

    // 测试getModel函数
    try {
      const modelInfo = this.getModel();
      console.log("🤖 获取模型信息:", modelInfo);
    } catch (error) {
      console.error("❌ getModel函数调用失败:", error);
      throw new Error(`getModel函数调用失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }

    this.chapterList = parseChaptersFromPlan(this.reportPlan);
    console.log("📚 解析得到章节列表:", this.chapterList);
    this.initializeChapters();
    console.log("✅ 章节初始化完成，当前章节索引:", this.currentChapterIndex);
  }

  /**
   * 初始化所有章节状态
   */
  private initializeChapters(): void {
    this.chapterList.forEach(chapter => {
      const chapterState: ChapterState = {
        id: chapter.id,
        goal: chapter.goal,
        status: 'pending',
        debateHistory: {
          discussions: [],
          currentRound: 1,
          totalTurns: 0
        },
        consensus: null,
        knowledgeSummary: '',
        queryResults: []
      };
      this.chapters.set(chapter.id, chapterState);
    });

    // 设置第一个章节为进行中
    if (this.chapterList.length > 0) {
      const firstChapter = this.chapters.get(this.chapterList[0].id);
      if (firstChapter) {
        firstChapter.status = 'in_progress';
      }
    }
  }

  /**
   * 获取当前章节
   */
  getCurrentChapter(): ChapterState | null {
    if (this.currentChapterIndex >= this.chapterList.length) {
      return null;
    }
    const chapterId = this.chapterList[this.currentChapterIndex].id;
    return this.chapters.get(chapterId) || null;
  }

  /**
   * 获取所有章节信息
   */
  getChapterList(): ChapterInfo[] {
    return this.chapterList;
  }

  /**
   * 获取章节状态
   */
  getChapterState(chapterId: string): ChapterState | null {
    return this.chapters.get(chapterId) || null;
  }

  /**
   * 开始章节研究
   */
  async startChapterResearch(): Promise<void> {
    console.log("🚀 开始章节研究，当前章节索引:", this.currentChapterIndex);
    console.log("📋 章节列表长度:", this.chapterList.length);
    const currentChapter = this.getCurrentChapter();
    console.log("📖 当前章节:", currentChapter);
    if (!currentChapter) {
      console.error("❌ 没有可研究的章节");
      throw new Error("没有可研究的章节");
    }

    this.onMessage("chapter_start", {
      chapterId: currentChapter.id,
      goal: currentChapter.goal,
      chapterIndex: this.currentChapterIndex + 1,
      totalChapters: this.chapterList.length
    });

    // 启动Alpha和Beta的首轮对话
    await this.initiateAgentDebate(currentChapter);
  }

  /**
   * 启动智能体辩论
   */
  private async initiateAgentDebate(chapter: ChapterState): Promise<void> {
    // Alpha先开始
    await this.processAgentTurn(chapter, 'Alpha');
  }

  /**
   * 处理智能体回合
   */
  private async processAgentTurn(
    chapter: ChapterState,
    agentId: 'Alpha' | 'Beta'
  ): Promise<void> {
    try {
      const discussionHistory = this.formatDiscussionHistory(chapter.debateHistory);

      // 计算发言状态信息
      const roundNumber = chapter.debateHistory.currentRound;
      const currentRoundDiscussions = chapter.debateHistory.discussions.filter(
        d => d.round === roundNumber
      );
      const turnInRound = currentRoundDiscussions.length + 1;

      // 获取对方最后的动作
      const lastOpponentDiscussion = chapter.debateHistory.discussions
        .filter(d => d.agentId !== agentId)
        .pop();
      const lastOpponentAction = lastOpponentDiscussion?.action.actionName;

      // 检查是否有待确认的查询
      const pendingQueries = lastOpponentAction === 'EXECUTE_QUERIES'
        ? lastOpponentDiscussion?.action.payload.queries
        : undefined;

      const prompt = generateChapterResearchPrompt(
        agentId,
        chapter.goal,
        discussionHistory,
        chapter.knowledgeSummary,
        roundNumber,
        turnInRound,
        lastOpponentAction,
        pendingQueries
      );

      this.onMessage("agent_thinking", {
        chapterId: chapter.id,
        agentId,
        round: chapter.debateHistory.currentRound,
        turn: chapter.debateHistory.totalTurns + 1
      });

      // 调用AI模型
      let text: string;
      try {
        const { thinkingModel } = this.getModel();
        const result = await generateText({
          model: await this.createModelProvider(thinkingModel),
          system: this.getSystemPrompt(),
          prompt,
        });
        text = result.text;
      } catch (error) {
        console.error(`Agent ${agentId} API调用失败:`, error);
        this.onMessage("agent_error", {
          agentId,
          error: `API调用失败: ${error instanceof Error ? error.message : '未知错误'}。请检查API配置和网络连接。`
        });
        throw error;
      }

      // 解析AI响应
      const response = this.parseAgentResponse(text);

      // 验证动作是否符合约束条件
      const validation = this.validateAgentAction(chapter, agentId, response.action);
      if (!validation.valid) {
        console.warn(`⚠️ 动作验证失败: ${validation.reason}`);
        // 强制修改为CONTINUE_DISCUSSION
        response.action = {
          actionName: 'CONTINUE_DISCUSSION',
          payload: {
            rationale: `动作被系统修正：${validation.reason}`
          }
        };
      }

      // 记录讨论
      const discussion: Discussion = {
        round: chapter.debateHistory.currentRound,
        turn: chapter.debateHistory.totalTurns + 1,
        agentId,
        thought: response.thought,
        speech: response.speech,
        action: response.action
      };

      chapter.debateHistory.discussions.push(discussion);
      chapter.debateHistory.totalTurns++;

      this.onMessage("agent_response", {
        chapterId: chapter.id,
        discussion,
        round: chapter.debateHistory.currentRound
      });

      // 处理动作
      await this.handleAgentAction(chapter, discussion);

    } catch (error) {
      console.error(`Agent ${agentId} 处理失败:`, error);
      this.onMessage("agent_error", {
        chapterId: chapter.id,
        agentId,
        error: error instanceof Error ? error.message : "未知错误"
      });
      // 重新抛出错误，让上层知道发生了错误
      throw error;
    }
  }

  /**
   * 解析智能体响应
   */
  private parseAgentResponse(text: string): {
    thought: string;
    speech: string;
    action: AgentAction;
  } {
    try {
      // 清理markdown代码块标记
      let cleanText = text.trim();

      // 移除开头的```json或```
      if (cleanText.startsWith('```json')) {
        cleanText = cleanText.substring(7);
      } else if (cleanText.startsWith('```')) {
        cleanText = cleanText.substring(3);
      }

      // 移除结尾的```
      if (cleanText.endsWith('```')) {
        cleanText = cleanText.substring(0, cleanText.length - 3);
      }

      cleanText = cleanText.trim();

      const response = JSON.parse(cleanText);
      return {
        thought: response.thought || "",
        speech: response.speech || "",
        action: response.action || { actionName: "CONTINUE_DISCUSSION", payload: {} }
      };
    } catch (error) {
      console.error("解析AI响应失败:", error);
      console.error("原始文本:", text);
      // 返回默认响应
      return {
        thought: "解析响应时出现错误",
        speech: "我需要重新组织我的想法",
        action: {
          actionName: "CONTINUE_DISCUSSION",
          payload: { rationale: "响应解析失败，需要继续讨论" }
        }
      };
    }
  }

  /**
   * 处理智能体动作
   */
  private async handleAgentAction(
    chapter: ChapterState,
    discussion: Discussion
  ): Promise<void> {
    const { action } = discussion;

    switch (action.actionName) {
      case 'CONTINUE_DISCUSSION':
        // 切换到另一个智能体
        const nextAgent = discussion.agentId === 'Alpha' ? 'Beta' : 'Alpha';
        await this.processAgentTurn(chapter, nextAgent);
        break;

      case 'EXECUTE_QUERIES':
        // 检查是否达成共识
        if (await this.checkConsensus()) {
          await this.executeConsensusQueries(chapter, action.payload.queries || []);
        } else {
          // 没有达成共识，继续讨论
          const nextAgent = discussion.agentId === 'Alpha' ? 'Beta' : 'Alpha';
          await this.processAgentTurn(chapter, nextAgent);
        }
        break;

      case 'CONCLUDE_CHAPTER':
        // 检查是否达成共识结束章节
        if (await this.checkConsensus()) {
          await this.concludeChapter(chapter, action.payload.justification || "");
        } else {
          // 没有达成共识，继续讨论
          const nextAgent = discussion.agentId === 'Alpha' ? 'Beta' : 'Alpha';
          await this.processAgentTurn(chapter, nextAgent);
        }
        break;
    }
  }

  /**
   * 检查是否达成共识
   */
  private async checkConsensus(): Promise<boolean> {
    // 简化版共识检查：如果是EXECUTE_QUERIES或CONCLUDE_CHAPTER，
    // 需要另一个智能体确认
    // 这里可以实现更复杂的共识机制
    return true; // 暂时总是返回true，后续可以优化
  }

  /**
   * 检测查询语言
   */
  private detectLanguage(query: string): string {
    // 简单的语言检测：如果包含中文字符，则为中文，否则为英文
    const chineseRegex = /[\u4e00-\u9fff]/;
    return chineseRegex.test(query) ? 'chinese' : 'english';
  }

  /**
   * 验证智能体动作是否符合约束条件
   */
  private validateAgentAction(
    chapter: ChapterState,
    agentId: 'Alpha' | 'Beta',
    action: AgentAction
  ): { valid: boolean; reason?: string } {
    const roundNumber = chapter.debateHistory.currentRound;
    const currentRoundDiscussions = chapter.debateHistory.discussions.filter(
      d => d.round === roundNumber
    );
    const turnInRound = currentRoundDiscussions.length + 1;

    // 规则1: 第一个发言者不能执行查询
    if (turnInRound === 1 && action.actionName === 'EXECUTE_QUERIES') {
      return {
        valid: false,
        reason: `${agentId}是第${roundNumber}轮第1个发言者，不能执行查询动作`
      };
    }

    // 规则2: 只有对方提议查询后才能执行查询
    if (action.actionName === 'EXECUTE_QUERIES') {
      const lastOpponentDiscussion = chapter.debateHistory.discussions
        .filter(d => d.agentId !== agentId)
        .pop();

      if (!lastOpponentDiscussion || lastOpponentDiscussion.action.actionName !== 'EXECUTE_QUERIES') {
        return {
          valid: false,
          reason: `${agentId}尝试执行查询，但对方没有提议查询`
        };
      }
    }

    return { valid: true };
  }

  /**
   * 执行共识查询
   */
  private async executeConsensusQueries(
    chapter: ChapterState,
    queries: ConsensusQuery[]
  ): Promise<void> {
    const consensus: ConsensusResult = {
      hasConsensus: true,
      queries: queries,
      reasoning: "双方达成共识执行查询"
    };

    chapter.consensus = consensus;

    this.onMessage("consensus_reached", {
      chapterId: chapter.id,
      consensus,
      queries
    });

    // 执行实际的搜索查询
    try {
      // 转换查询格式为搜索任务
      const searchTasks = queries.map((query) => ({
        query: query.query,
        language: this.detectLanguage(query.query) as "chinese" | "english",
        researchGoal: query.researchGoal,
        learning: "",
        state: "unprocessed" as const,
        sources: [],
        images: [],
        roundNumber: chapter.debateHistory.currentRound,
        taskType: "search" as const,
      }));

      // 发送查询执行消息
      this.onMessage("execute_queries", {
        chapterId: chapter.id,
        queries: searchTasks
      });

      // 等待查询完成后进入下一轮讨论
      // 这里可以添加查询完成的回调处理
      chapter.debateHistory.currentRound++;
      await this.processAgentTurn(chapter, 'Alpha');
    } catch (error) {
      console.error("执行查询失败:", error);
      // 即使查询失败，也继续下一轮讨论
      chapter.debateHistory.currentRound++;
      await this.processAgentTurn(chapter, 'Alpha');
    }
  }

  /**
   * 结束章节
   */
  private async concludeChapter(
    chapter: ChapterState,
    justification: string
  ): Promise<void> {
    chapter.status = 'completed';
    
    this.onMessage("chapter_completed", {
      chapterId: chapter.id,
      justification,
      totalRounds: chapter.debateHistory.currentRound,
      totalTurns: chapter.debateHistory.totalTurns
    });

    // 切换到下一章节
    await this.moveToNextChapter();
  }

  /**
   * 切换到下一章节
   */
  private async moveToNextChapter(): Promise<void> {
    this.currentChapterIndex++;
    
    if (this.currentChapterIndex >= this.chapterList.length) {
      // 所有章节完成
      this.onMessage("research_completed", {
        totalChapters: this.chapterList.length,
        completedChapters: this.currentChapterIndex
      });
      return;
    }

    // 开始下一章节
    const nextChapter = this.getCurrentChapter();
    if (nextChapter) {
      nextChapter.status = 'in_progress';
      await this.startChapterResearch();
    }
  }

  /**
   * 格式化讨论历史
   */
  private formatDiscussionHistory(debateHistory: DebateHistory): string {
    if (debateHistory.discussions.length === 0) {
      return "这是本章节的第一次讨论。";
    }

    return debateHistory.discussions
      .map(d => `[第${d.round}轮-第${d.turn}次] ${d.agentId}: ${d.speech}`)
      .join('\n');
  }
}
