// 章节式研究管理器
import { generateText } from "ai";
import {
  ChapterInfo,
  ChapterState,
  Discussion,
  AgentAction,
  ConsensusResult,
  ConsensusQuery,
  // DebateHistory, // 暂时不使用
  generateOpeningPrompt,
  generateHighLevelResponsePrompt,
  generateQueryStartPrompt,
  generateQueryResponsePrompt,
  generateChapterEndPrompt,
  parseChaptersFromPlan
} from "./auto-research-prompts";

/**
 * 章节研究配置参数
 */
interface ChapterResearchConfig {
  // 循环控制参数
  maxHighLevelLoops: number;        // 高层级讨论最大循环次数
  maxQueryLoops: number;            // 查询讨论最大循环次数
  roundThreshold: number;           // 章节轮数阈值
  maxRounds: number;                // 章节最大轮数
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: ChapterResearchConfig = {
  maxHighLevelLoops: 3,
  maxQueryLoops: 3,
  roundThreshold: 2,
  maxRounds: 5
};

/**
 * 章节研究管理器
 * 负责管理整个章节式研究流程
 */
export class ChapterResearchManager {
  private chapters: Map<string, ChapterState> = new Map();
  private currentChapterIndex: number = 0;
  private config: ChapterResearchConfig;
  private chapterList: ChapterInfo[] = [];

  constructor(
    private reportPlan: string,
    private onMessage: (type: string, data: any) => void,
    private createModelProvider: (model: string) => Promise<any>,
    private getSystemPrompt: () => string,
    private getModel: () => { thinkingModel: string; networkingModel: string },
    config?: Partial<ChapterResearchConfig>
  ) {
    // 初始化配置
    this.config = { ...DEFAULT_CONFIG, ...config };
    console.log("🔍 开始解析研究计划:", this.reportPlan);

    // 测试getModel函数
    try {
      const modelInfo = this.getModel();
      console.log("🤖 获取模型信息:", modelInfo);
    } catch (error) {
      console.error("❌ getModel函数调用失败:", error);
      throw new Error(`getModel函数调用失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }

    this.chapterList = parseChaptersFromPlan(this.reportPlan);
    console.log("📚 解析得到章节列表:", this.chapterList);
    this.initializeChapters();
    console.log("✅ 章节初始化完成，当前章节索引:", this.currentChapterIndex);
  }

  /**
   * 初始化所有章节状态
   */
  private initializeChapters(): void {
    this.chapterList.forEach(chapter => {
      const chapterState: ChapterState = {
        id: chapter.id,
        goal: chapter.goal,
        status: 'pending',
        debateHistory: {
          discussions: [],
          currentRound: 1,
          totalTurns: 0
        },
        consensus: null,
        knowledgeSummary: '',
        queryResults: [],

        // 状态机控制字段
        currentPhase: 'HIGH_LEVEL',
        highLevelLoopCount: 0,
        queryLoopCount: 0
      };
      this.chapters.set(chapter.id, chapterState);
    });

    // 设置第一个章节为进行中
    if (this.chapterList.length > 0) {
      const firstChapter = this.chapters.get(this.chapterList[0].id);
      if (firstChapter) {
        firstChapter.status = 'in_progress';
      }
    }
  }

  /**
   * 获取当前章节
   */
  getCurrentChapter(): ChapterState | null {
    if (this.currentChapterIndex >= this.chapterList.length) {
      return null;
    }
    const chapterId = this.chapterList[this.currentChapterIndex].id;
    return this.chapters.get(chapterId) || null;
  }

  /**
   * 获取所有章节信息
   */
  getChapterList(): ChapterInfo[] {
    return this.chapterList;
  }

  /**
   * 获取章节状态
   */
  getChapterState(chapterId: string): ChapterState | null {
    return this.chapters.get(chapterId) || null;
  }

  /**
   * 开始章节研究
   */
  async startChapterResearch(): Promise<void> {
    console.log("🚀 开始章节研究，当前章节索引:", this.currentChapterIndex);
    console.log("📋 章节列表长度:", this.chapterList.length);
    const currentChapter = this.getCurrentChapter();
    console.log("📖 当前章节:", currentChapter);
    if (!currentChapter) {
      console.error("❌ 没有可研究的章节");
      throw new Error("没有可研究的章节");
    }

    this.onMessage("chapter_start", {
      chapterId: currentChapter.id,
      goal: currentChapter.goal,
      chapterIndex: this.currentChapterIndex + 1,
      totalChapters: this.chapterList.length
    });

    // 启动Alpha和Beta的首轮对话
    await this.initiateAgentDebate(currentChapter);
  }

  /**
   * 启动智能体辩论
   */
  private async initiateAgentDebate(chapter: ChapterState): Promise<void> {
    // Alpha先开始
    await this.processAgentTurn(chapter, 'Alpha');
  }

  /**
   * 处理智能体回合 - 基于状态机逻辑
   */
  private async processAgentTurn(
    chapter: ChapterState,
    agentId: 'Alpha' | 'Beta'
  ): Promise<void> {
    try {
      // 计算发言状态信息
      const roundNumber = chapter.debateHistory.currentRound;
      const currentRoundDiscussions = chapter.debateHistory.discussions.filter(
        d => d.round === roundNumber
      );
      const turnInRound = currentRoundDiscussions.length + 1;

      // 获取对方在当前轮次的最后动作
      const lastOpponentDiscussion = chapter.debateHistory.discussions
        .filter(d => d.agentId !== agentId && d.round === roundNumber)
        .pop();
      const lastOpponentAction = lastOpponentDiscussion?.action.actionName;

      // 状态机逻辑：根据当前状态和循环计数决定prompt和强制动作
      let prompt: string = "";
      let forcedAction: AgentAction | null = null;

      if (turnInRound === 1 && roundNumber === 1) {
        // 开场AI
        chapter.currentPhase = 'HIGH_LEVEL';
        prompt = generateOpeningPrompt(
          agentId,
          chapter.goal,
          chapter.knowledgeSummary
        );
      } else if (lastOpponentAction === 'HIGH_LEVEL_PROPOSE') {
        // 高层级响应AI
        chapter.highLevelLoopCount++;

        // 检查是否达到最大循环次数
        if (chapter.highLevelLoopCount >= this.config.maxHighLevelLoops) {
          // 强制同意
          forcedAction = {
            actionName: 'HIGH_LEVEL_AGREE',
            payload: {
              agreement: `已达到最大讨论次数(${this.config.maxHighLevelLoops})，强制同意对方提议`
            }
          };
        } else {
          const currentRoundHistory = this.formatCurrentRoundDiscussions(currentRoundDiscussions);
          prompt = generateHighLevelResponsePrompt(
            agentId,
            chapter.goal,
            currentRoundHistory,
            chapter.knowledgeSummary
          );
        }
      } else if (lastOpponentAction === 'HIGH_LEVEL_AGREE') {
        // 查询开始AI
        chapter.currentPhase = 'QUERY';
        chapter.highLevelConsensus = this.extractHighLevelConsensus(currentRoundDiscussions);
        chapter.queryLoopCount = 0; // 重置查询循环计数

        prompt = generateQueryStartPrompt(
          agentId,
          chapter.goal,
          chapter.highLevelConsensus,
          chapter.knowledgeSummary
        );
      } else if (lastOpponentAction === 'QUERY_PROPOSE') {
        // 查询响应AI
        chapter.queryLoopCount++;

        // 检查是否达到最大循环次数
        if (chapter.queryLoopCount >= this.config.maxQueryLoops) {
          // 强制执行查询
          const lastQuery = lastOpponentDiscussion?.action.payload.queries || [];
          forcedAction = {
            actionName: 'EXECUTE_QUERIES',
            payload: {
              queries: lastQuery
            }
          };
        } else {
          const currentQueryHistory = this.formatCurrentQueryDiscussions(currentRoundDiscussions);
          prompt = generateQueryResponsePrompt(
            agentId,
            chapter.goal,
            chapter.highLevelConsensus || "",
            currentQueryHistory,
            chapter.knowledgeSummary
          );
        }
      } else {
        // 默认使用开场prompt
        prompt = generateOpeningPrompt(
          agentId,
          chapter.goal,
          chapter.knowledgeSummary
        );
      }

      this.onMessage("agent_thinking", {
        chapterId: chapter.id,
        agentId,
        round: chapter.debateHistory.currentRound,
        turn: chapter.debateHistory.totalTurns + 1
      });

      // 发送状态机信息更新
      this.onMessage("state_machine_update", {
        chapterId: chapter.id,
        currentPhase: chapter.currentPhase,
        highLevelLoopCount: chapter.highLevelLoopCount,
        queryLoopCount: chapter.queryLoopCount,
        currentRound: chapter.debateHistory.currentRound
      });

      // 处理强制动作或调用AI模型
      let response: { thought: string; action: AgentAction };

      if (forcedAction) {
        // 使用强制动作
        response = {
          thought: `系统强制执行动作：${forcedAction.actionName}。原因：已达到最大循环次数限制。`,
          action: forcedAction
        };
        console.log(`🔒 强制执行动作: ${agentId} -> ${forcedAction.actionName}`);
      } else {
        // 调用AI模型
        let text: string;
        try {
          const { thinkingModel } = this.getModel();
          const result = await generateText({
            model: await this.createModelProvider(thinkingModel),
            system: this.getSystemPrompt(),
            prompt,
          });
          text = result.text;
        } catch (error) {
          console.error(`Agent ${agentId} API调用失败:`, error);
          this.onMessage("agent_error", {
            agentId,
            error: `API调用失败: ${error instanceof Error ? error.message : '未知错误'}。请检查API配置和网络连接。`
          });
          throw error;
        }

        // 解析AI响应
        response = this.parseAgentResponse(text);
      }

      // 验证动作是否符合约束条件
      const validation = this.validateAgentAction(chapter, agentId, response.action);
      if (!validation.valid) {
        console.warn(`⚠️ 动作验证失败: ${validation.reason}`);
        // 强制修改为HIGH_LEVEL_PROPOSE
        response.action = {
          actionName: 'HIGH_LEVEL_PROPOSE',
          payload: {
            proposal: `动作被系统修正：${validation.reason}`
          }
        };
      }

      // 记录讨论
      const discussion: Discussion = {
        round: chapter.debateHistory.currentRound,
        turn: chapter.debateHistory.totalTurns + 1,
        agentId,
        thought: response.thought,
        action: response.action
      };

      chapter.debateHistory.discussions.push(discussion);
      chapter.debateHistory.totalTurns++;

      this.onMessage("agent_response", {
        chapterId: chapter.id,
        discussion,
        round: chapter.debateHistory.currentRound
      });

      // 处理动作
      await this.handleAgentAction(chapter, discussion);

    } catch (error) {
      console.error(`Agent ${agentId} 处理失败:`, error);
      this.onMessage("agent_error", {
        chapterId: chapter.id,
        agentId,
        error: error instanceof Error ? error.message : "未知错误"
      });
      // 重新抛出错误，让上层知道发生了错误
      throw error;
    }
  }

  /**
   * 解析智能体响应
   */
  private parseAgentResponse(text: string): {
    thought: string;
    action: AgentAction;
  } {
    try {
      // 清理markdown代码块标记
      let cleanText = text.trim();

      // 移除开头的```json或```
      if (cleanText.startsWith('```json')) {
        cleanText = cleanText.substring(7);
      } else if (cleanText.startsWith('```')) {
        cleanText = cleanText.substring(3);
      }

      // 移除结尾的```
      if (cleanText.endsWith('```')) {
        cleanText = cleanText.substring(0, cleanText.length - 3);
      }

      cleanText = cleanText.trim();

      const response = JSON.parse(cleanText);

      // 确保thought字段是字符串
      let thoughtString = "";
      if (typeof response.thought === 'string') {
        thoughtString = response.thought;
      } else if (response.thought && typeof response.thought === 'object') {
        // 如果thought是对象，尝试转换为字符串
        thoughtString = JSON.stringify(response.thought, null, 2);
        console.warn("⚠️ AI返回的thought字段是对象，已转换为字符串:", response.thought);
      } else {
        thoughtString = String(response.thought || "");
      }

      return {
        thought: thoughtString,
        action: response.action || { actionName: "HIGH_LEVEL_PROPOSE", payload: {} }
      };
    } catch (error) {
      console.error("解析AI响应失败:", error);
      console.error("原始文本:", text);
      // 返回默认响应
      return {
        thought: "解析响应时出现错误",
        action: {
          actionName: "HIGH_LEVEL_PROPOSE",
          payload: { proposal: "响应解析失败，需要重新提议" }
        }
      };
    }
  }

  /**
   * 处理智能体动作
   */
  private async handleAgentAction(
    chapter: ChapterState,
    discussion: Discussion
  ): Promise<void> {
    const { action } = discussion;

    switch (action.actionName) {
      case 'HIGH_LEVEL_PROPOSE':
        // 高层级提议，切换到另一个智能体
        const nextAgentForPropose = discussion.agentId === 'Alpha' ? 'Beta' : 'Alpha';
        await this.processAgentTurn(chapter, nextAgentForPropose);
        break;

      case 'HIGH_LEVEL_AGREE':
        // 高层级赞同，进入查询提议阶段
        // 可以继续让同一个智能体提议查询，或切换到对方
        const nextAgentForQuery = discussion.agentId === 'Alpha' ? 'Beta' : 'Alpha';
        await this.processAgentTurn(chapter, nextAgentForQuery);
        break;

      case 'QUERY_PROPOSE':
        // 查询提议，切换到另一个智能体决定是否执行
        const nextAgentForDecision = discussion.agentId === 'Alpha' ? 'Beta' : 'Alpha';
        await this.processAgentTurn(chapter, nextAgentForDecision);
        break;

      case 'EXECUTE_QUERIES':
        // 执行查询，然后进入下一轮讨论
        await this.executeConsensusQueries(chapter, action.payload.queries || []);
        break;

      case 'CHAPTER_END_PROPOSE':
        // 章节结束提议，切换到另一个智能体
        const nextAgentForEnd = discussion.agentId === 'Alpha' ? 'Beta' : 'Alpha';
        await this.processAgentTurn(chapter, nextAgentForEnd);
        break;

      case 'CHAPTER_END_AGREE':
        // 章节结束赞同，结束当前章节
        await this.concludeChapter(chapter, action.payload.justification || "双方同意结束章节");
        break;
    }
  }

  /**
   * 检查是否达成共识
   */
  private async checkConsensus(): Promise<boolean> {
    // 简化版共识检查：如果是EXECUTE_QUERIES或CONCLUDE_CHAPTER，
    // 需要另一个智能体确认
    // 这里可以实现更复杂的共识机制
    return true; // 暂时总是返回true，后续可以优化
  }

  /**
   * 检测查询语言
   */
  private detectLanguage(query: string): string {
    // 简单的语言检测：如果包含中文字符，则为中文，否则为英文
    const chineseRegex = /[\u4e00-\u9fff]/;
    return chineseRegex.test(query) ? 'chinese' : 'english';
  }

  /**
   * 验证智能体动作是否符合约束条件
   */
  private validateAgentAction(
    chapter: ChapterState,
    agentId: 'Alpha' | 'Beta',
    action: AgentAction
  ): { valid: boolean; reason?: string } {
    const roundNumber = chapter.debateHistory.currentRound;
    const currentRoundDiscussions = chapter.debateHistory.discussions.filter(
      d => d.round === roundNumber
    );
    const turnInRound = currentRoundDiscussions.length + 1;

    // 获取对方最后的动作（暂时不使用，但保留以备后续扩展）
    // const lastOpponentDiscussion = chapter.debateHistory.discussions
    //   .filter(d => d.agentId !== agentId)
    //   .pop();
    // const lastOpponentAction = lastOpponentDiscussion?.action.actionName;

    // 规则1: Alpha开场建议使用HIGH_LEVEL_PROPOSE（但不强制）
    if (turnInRound === 1 && roundNumber === 1 && agentId === 'Alpha') {
      // 只是建议，不强制验证，让AI自由选择
    }

    // 规则2: 第一个发言者不能直接执行查询
    if (turnInRound === 1 && action.actionName === 'EXECUTE_QUERIES') {
      return {
        valid: false,
        reason: `第一个发言者不能直接执行查询，需要先讨论`
      };
    }

    return { valid: true };
  }



  /**
   * 执行共识查询
   */
  private async executeConsensusQueries(
    chapter: ChapterState,
    queries: ConsensusQuery[]
  ): Promise<void> {
    const consensus: ConsensusResult = {
      hasConsensus: true,
      queries: queries,
      reasoning: "双方达成共识执行查询"
    };

    chapter.consensus = consensus;

    this.onMessage("consensus_reached", {
      chapterId: chapter.id,
      consensus,
      queries
    });

    // 执行实际的搜索查询
    try {
      // 转换查询格式为搜索任务
      const searchTasks = queries.map((query) => ({
        query: query.query,
        language: this.detectLanguage(query.query) as "chinese" | "english",
        researchGoal: query.researchGoal,
        learning: "",
        state: "unprocessed" as const,
        sources: [],
        images: [],
        roundNumber: chapter.debateHistory.currentRound,
        taskType: "search" as const,
      }));

      // 发送查询执行消息
      this.onMessage("execute_queries", {
        chapterId: chapter.id,
        queries: searchTasks
      });

      // 等待查询完成后进入下一轮讨论
      // 这里可以添加查询完成的回调处理
      await this.startNextRound(chapter);
    } catch (error) {
      console.error("执行查询失败:", error);
      // 即使查询失败，也继续下一轮讨论
      await this.startNextRound(chapter);
    }
  }

  /**
   * 开始下一轮讨论或检查章节结束
   */
  private async startNextRound(chapter: ChapterState): Promise<void> {
    chapter.debateHistory.currentRound++;

    // 重置循环计数器
    chapter.currentPhase = 'HIGH_LEVEL';
    chapter.highLevelLoopCount = 0;
    chapter.queryLoopCount = 0;

    this.onMessage("round_start", {
      chapterId: chapter.id,
      round: chapter.debateHistory.currentRound
    });

    // 检查是否达到轮数阈值，需要进行章节结束判断
    if (chapter.debateHistory.currentRound > this.config.roundThreshold) {
      const shouldEnd = await this.checkChapterEnd(chapter);
      if (shouldEnd) {
        await this.concludeChapter(chapter, "AI判断章节目标已充分达成");
        return;
      }
    }

    // 检查是否达到最大轮数，强制结束
    if (chapter.debateHistory.currentRound > this.config.maxRounds) {
      await this.concludeChapter(chapter, `已达到最大轮数限制(${this.config.maxRounds})，强制结束章节`);
      return;
    }

    // 继续下一轮讨论，从Alpha开始
    await this.processAgentTurn(chapter, 'Alpha');
  }

  /**
   * 检查章节是否应该结束
   */
  private async checkChapterEnd(chapter: ChapterState): Promise<boolean> {
    try {
      // 格式化所有轮次的讨论历史
      const allRoundDiscussions = this.formatAllRoundDiscussions(chapter.debateHistory.discussions);

      // 生成章节结束判断prompt
      const prompt = generateChapterEndPrompt(
        chapter.goal,
        allRoundDiscussions,
        chapter.knowledgeSummary
      );

      // 调用AI进行判断
      const { thinkingModel } = this.getModel();
      const result = await generateText({
        model: await this.createModelProvider(thinkingModel),
        system: this.getSystemPrompt(),
        prompt,
      });

      // 解析判断结果
      try {
        const response = JSON.parse(result.text);
        const shouldEnd = response.shouldEnd === true;
        const reason = response.reason || "未提供理由";

        console.log(`📊 章节结束判断: ${shouldEnd ? '是' : '否'}, 理由: ${reason}`);

        this.onMessage("chapter_end_judgment", {
          chapterId: chapter.id,
          shouldEnd,
          reason
        });

        return shouldEnd;
      } catch (parseError) {
        console.error("解析章节结束判断结果失败:", parseError);
        // 解析失败时默认不结束
        return false;
      }
    } catch (error) {
      console.error("章节结束判断失败:", error);
      // 判断失败时默认不结束
      return false;
    }
  }

  /**
   * 结束章节
   */
  private async concludeChapter(
    chapter: ChapterState,
    justification: string
  ): Promise<void> {
    chapter.status = 'completed';
    
    this.onMessage("chapter_completed", {
      chapterId: chapter.id,
      justification,
      totalRounds: chapter.debateHistory.currentRound,
      totalTurns: chapter.debateHistory.totalTurns
    });

    // 切换到下一章节
    await this.moveToNextChapter();
  }

  /**
   * 切换到下一章节
   */
  private async moveToNextChapter(): Promise<void> {
    this.currentChapterIndex++;
    
    if (this.currentChapterIndex >= this.chapterList.length) {
      // 所有章节完成
      this.onMessage("research_completed", {
        totalChapters: this.chapterList.length,
        completedChapters: this.currentChapterIndex
      });
      return;
    }

    // 开始下一章节
    const nextChapter = this.getCurrentChapter();
    if (nextChapter) {
      nextChapter.status = 'in_progress';
      await this.startChapterResearch();
    }
  }

  /**
   * 格式化讨论历史 - 暂时不使用
   */
  // private formatDiscussionHistory(debateHistory: DebateHistory): string {
  //   if (debateHistory.discussions.length === 0) {
  //     return "这是本章节的第一次讨论。";
  //   }

  //   return debateHistory.discussions
  //     .map(d => `[第${d.round}轮-第${d.turn}次] ${d.agentId}: ${d.action.actionName}`)
  //     .join('\n');
  // }

  /**
   * 格式化当前轮次讨论历史
   */
  private formatCurrentRoundDiscussions(discussions: Discussion[]): string {
    if (discussions.length === 0) {
      return "当前轮次暂无讨论";
    }

    return discussions
      .map(d => `${d.agentId}: ${d.action.actionName} - ${JSON.stringify(d.action.payload)}`)
      .join('\n');
  }

  /**
   * 提取高层级共识
   */
  private extractHighLevelConsensus(discussions: Discussion[]): string {
    const agreeDiscussion = discussions.find(d => d.action.actionName === 'HIGH_LEVEL_AGREE');
    if (agreeDiscussion) {
      return agreeDiscussion.action.payload.agreement || "已达成高层级共识";
    }

    const proposeDiscussion = discussions.find(d => d.action.actionName === 'HIGH_LEVEL_PROPOSE');
    if (proposeDiscussion) {
      return proposeDiscussion.action.payload.proposal || "高层级提议";
    }

    return "暂无高层级共识";
  }

  /**
   * 格式化当前查询讨论历史
   */
  private formatCurrentQueryDiscussions(discussions: Discussion[]): string {
    const queryDiscussions = discussions.filter(d =>
      d.action.actionName === 'QUERY_PROPOSE' || d.action.actionName === 'EXECUTE_QUERIES'
    );

    if (queryDiscussions.length === 0) {
      return "当前轮次暂无查询讨论";
    }

    return queryDiscussions
      .map(d => `${d.agentId}: ${d.action.actionName} - ${JSON.stringify(d.action.payload)}`)
      .join('\n');
  }

  /**
   * 格式化所有轮次的讨论历史
   */
  private formatAllRoundDiscussions(discussions: Discussion[]): string {
    if (discussions.length === 0) {
      return "暂无讨论历史";
    }

    // 按轮次分组
    const roundGroups = discussions.reduce((groups, discussion) => {
      const round = discussion.round;
      if (!groups[round]) {
        groups[round] = [];
      }
      groups[round].push(discussion);
      return groups;
    }, {} as Record<number, Discussion[]>);

    // 格式化每轮讨论
    const roundSummaries = Object.keys(roundGroups)
      .sort((a, b) => parseInt(a) - parseInt(b))
      .map(roundStr => {
        const round = parseInt(roundStr);
        const roundDiscussions = roundGroups[round];
        const roundSummary = roundDiscussions
          .map(d => `  ${d.agentId}: ${d.action.actionName} - ${JSON.stringify(d.action.payload)}`)
          .join('\n');
        return `第${round}轮讨论:\n${roundSummary}`;
      });

    return roundSummaries.join('\n\n');
  }
}
