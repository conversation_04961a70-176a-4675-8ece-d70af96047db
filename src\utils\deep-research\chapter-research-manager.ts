// 章节式研究管理器
import { generateText } from "ai";
import {
  ChapterInfo,
  ChapterState,
  Discussion,
  AgentAction,
  ConsensusResult,
  ConsensusQuery,
  DebateHistory,
  generateChapterResearchPrompt,
  parseChaptersFromPlan
} from "./auto-research-prompts";

/**
 * 章节研究管理器
 * 负责管理整个章节式研究流程
 */
export class ChapterResearchManager {
  private chapters: Map<string, ChapterState> = new Map();
  private currentChapterIndex: number = 0;
  private chapterList: ChapterInfo[] = [];

  constructor(
    private reportPlan: string,
    private onMessage: (type: string, data: any) => void,
    private createModelProvider: (model: string) => Promise<any>,
    private getSystemPrompt: () => string
  ) {
    console.log("🔍 开始解析研究计划:", this.reportPlan);
    this.chapterList = parseChaptersFromPlan(this.reportPlan);
    console.log("📚 解析得到章节列表:", this.chapterList);
    this.initializeChapters();
    console.log("✅ 章节初始化完成，当前章节索引:", this.currentChapterIndex);
  }

  /**
   * 初始化所有章节状态
   */
  private initializeChapters(): void {
    this.chapterList.forEach(chapter => {
      const chapterState: ChapterState = {
        id: chapter.id,
        goal: chapter.goal,
        status: 'pending',
        debateHistory: {
          discussions: [],
          currentRound: 1,
          totalTurns: 0
        },
        consensus: null,
        knowledgeSummary: '',
        queryResults: []
      };
      this.chapters.set(chapter.id, chapterState);
    });

    // 设置第一个章节为进行中
    if (this.chapterList.length > 0) {
      const firstChapter = this.chapters.get(this.chapterList[0].id);
      if (firstChapter) {
        firstChapter.status = 'in_progress';
      }
    }
  }

  /**
   * 获取当前章节
   */
  getCurrentChapter(): ChapterState | null {
    if (this.currentChapterIndex >= this.chapterList.length) {
      return null;
    }
    const chapterId = this.chapterList[this.currentChapterIndex].id;
    return this.chapters.get(chapterId) || null;
  }

  /**
   * 获取所有章节信息
   */
  getChapterList(): ChapterInfo[] {
    return this.chapterList;
  }

  /**
   * 获取章节状态
   */
  getChapterState(chapterId: string): ChapterState | null {
    return this.chapters.get(chapterId) || null;
  }

  /**
   * 开始章节研究
   */
  async startChapterResearch(): Promise<void> {
    console.log("🚀 开始章节研究，当前章节索引:", this.currentChapterIndex);
    console.log("📋 章节列表长度:", this.chapterList.length);
    const currentChapter = this.getCurrentChapter();
    console.log("📖 当前章节:", currentChapter);
    if (!currentChapter) {
      console.error("❌ 没有可研究的章节");
      throw new Error("没有可研究的章节");
    }

    this.onMessage("chapter_start", {
      chapterId: currentChapter.id,
      goal: currentChapter.goal,
      chapterIndex: this.currentChapterIndex + 1,
      totalChapters: this.chapterList.length
    });

    // 启动Alpha和Beta的首轮对话
    await this.initiateAgentDebate(currentChapter);
  }

  /**
   * 启动智能体辩论
   */
  private async initiateAgentDebate(chapter: ChapterState): Promise<void> {
    // Alpha先开始
    await this.processAgentTurn(chapter, 'Alpha');
  }

  /**
   * 处理智能体回合
   */
  private async processAgentTurn(
    chapter: ChapterState,
    agentId: 'Alpha' | 'Beta'
  ): Promise<void> {
    try {
      const discussionHistory = this.formatDiscussionHistory(chapter.debateHistory);
      const prompt = generateChapterResearchPrompt(
        agentId,
        chapter.goal,
        discussionHistory,
        chapter.knowledgeSummary
      );

      this.onMessage("agent_thinking", {
        chapterId: chapter.id,
        agentId,
        round: chapter.debateHistory.currentRound,
        turn: chapter.debateHistory.totalTurns + 1
      });

      // 调用AI模型
      let text: string;
      try {
        const result = await generateText({
          model: await this.createModelProvider("gpt-4o"),
          system: this.getSystemPrompt(),
          prompt,
        });
        text = result.text;
      } catch (error) {
        console.error(`Agent ${agentId} API调用失败:`, error);
        this.onMessage("agent_error", {
          agentId,
          error: `API调用失败: ${error instanceof Error ? error.message : '未知错误'}。请检查API配置和网络连接。`
        });
        throw error;
      }

      // 解析AI响应
      const response = this.parseAgentResponse(text);
      
      // 记录讨论
      const discussion: Discussion = {
        round: chapter.debateHistory.currentRound,
        turn: chapter.debateHistory.totalTurns + 1,
        agentId,
        thought: response.thought,
        speech: response.speech,
        action: response.action,
        timestamp: Date.now()
      };

      chapter.debateHistory.discussions.push(discussion);
      chapter.debateHistory.totalTurns++;

      this.onMessage("agent_response", {
        chapterId: chapter.id,
        discussion
      });

      // 处理动作
      await this.handleAgentAction(chapter, discussion);

    } catch (error) {
      console.error(`Agent ${agentId} 处理失败:`, error);
      this.onMessage("agent_error", {
        chapterId: chapter.id,
        agentId,
        error: error instanceof Error ? error.message : "未知错误"
      });
      // 重新抛出错误，让上层知道发生了错误
      throw error;
    }
  }

  /**
   * 解析智能体响应
   */
  private parseAgentResponse(text: string): {
    thought: string;
    speech: string;
    action: AgentAction;
  } {
    try {
      const response = JSON.parse(text);
      return {
        thought: response.thought || "",
        speech: response.speech || "",
        action: response.action || { actionName: "CONTINUE_DISCUSSION", payload: {} }
      };
    } catch (error) {
      console.error("解析AI响应失败:", error);
      // 返回默认响应
      return {
        thought: "解析响应时出现错误",
        speech: "我需要重新组织我的想法",
        action: {
          actionName: "CONTINUE_DISCUSSION",
          payload: { rationale: "响应解析失败，需要继续讨论" }
        }
      };
    }
  }

  /**
   * 处理智能体动作
   */
  private async handleAgentAction(
    chapter: ChapterState,
    discussion: Discussion
  ): Promise<void> {
    const { action } = discussion;

    switch (action.actionName) {
      case 'CONTINUE_DISCUSSION':
        // 切换到另一个智能体
        const nextAgent = discussion.agentId === 'Alpha' ? 'Beta' : 'Alpha';
        await this.processAgentTurn(chapter, nextAgent);
        break;

      case 'EXECUTE_QUERIES':
        // 检查是否达成共识
        if (await this.checkConsensus()) {
          await this.executeConsensusQueries(chapter, action.payload.queries || []);
        } else {
          // 没有达成共识，继续讨论
          const nextAgent = discussion.agentId === 'Alpha' ? 'Beta' : 'Alpha';
          await this.processAgentTurn(chapter, nextAgent);
        }
        break;

      case 'CONCLUDE_CHAPTER':
        // 检查是否达成共识结束章节
        if (await this.checkConsensus()) {
          await this.concludeChapter(chapter, action.payload.justification || "");
        } else {
          // 没有达成共识，继续讨论
          const nextAgent = discussion.agentId === 'Alpha' ? 'Beta' : 'Alpha';
          await this.processAgentTurn(chapter, nextAgent);
        }
        break;
    }
  }

  /**
   * 检查是否达成共识
   */
  private async checkConsensus(): Promise<boolean> {
    // 简化版共识检查：如果是EXECUTE_QUERIES或CONCLUDE_CHAPTER，
    // 需要另一个智能体确认
    // 这里可以实现更复杂的共识机制
    return true; // 暂时总是返回true，后续可以优化
  }

  /**
   * 执行共识查询
   */
  private async executeConsensusQueries(
    chapter: ChapterState,
    queries: ConsensusQuery[]
  ): Promise<void> {
    const consensus: ConsensusResult = {
      agreedQueries: queries,
      justification: "双方达成共识执行查询",
      timestamp: Date.now(),
      participatingAgents: ['Alpha', 'Beta']
    };

    chapter.consensus = consensus;

    this.onMessage("consensus_reached", {
      chapterId: chapter.id,
      consensus,
      queries
    });

    // 这里应该调用实际的搜索执行逻辑
    // 暂时先进入下一轮讨论
    chapter.debateHistory.currentRound++;
    await this.processAgentTurn(chapter, 'Alpha');
  }

  /**
   * 结束章节
   */
  private async concludeChapter(
    chapter: ChapterState,
    justification: string
  ): Promise<void> {
    chapter.status = 'completed';
    
    this.onMessage("chapter_completed", {
      chapterId: chapter.id,
      justification,
      totalRounds: chapter.debateHistory.currentRound,
      totalTurns: chapter.debateHistory.totalTurns
    });

    // 切换到下一章节
    await this.moveToNextChapter();
  }

  /**
   * 切换到下一章节
   */
  private async moveToNextChapter(): Promise<void> {
    this.currentChapterIndex++;
    
    if (this.currentChapterIndex >= this.chapterList.length) {
      // 所有章节完成
      this.onMessage("research_completed", {
        totalChapters: this.chapterList.length,
        completedChapters: this.currentChapterIndex
      });
      return;
    }

    // 开始下一章节
    const nextChapter = this.getCurrentChapter();
    if (nextChapter) {
      nextChapter.status = 'in_progress';
      await this.startChapterResearch();
    }
  }

  /**
   * 格式化讨论历史
   */
  private formatDiscussionHistory(debateHistory: DebateHistory): string {
    if (debateHistory.discussions.length === 0) {
      return "这是本章节的第一次讨论。";
    }

    return debateHistory.discussions
      .map(d => `[第${d.round}轮-第${d.turn}次] ${d.agentId}: ${d.speech}`)
      .join('\n');
  }
}
