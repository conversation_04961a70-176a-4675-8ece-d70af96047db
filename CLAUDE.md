# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

Deep Research 是一个基于多种大语言模型的深度研究报告生成平台，支持快速生成综合性研究报告。项目采用 Next.js 15 架构，支持多种AI模型、搜索引擎集成，并提供SSE API和MCP服务。

## 开发命令

### 基础开发
```bash
npm run dev          # 开发服务器 (端口3001)
npm run build        # 生产构建
npm run start        # 启动生产服务器
npm run lint         # ESLint检查
```

### 特殊构建模式
```bash
npm run build:standalone  # 独立部署构建
npm run build:export     # 静态导出构建
```

## 核心架构

### 主要技术栈
- **框架**: Next.js 15 (App Router)
- **UI**: shadcn/ui + Tailwind CSS
- **状态管理**: Zustand
- **AI SDK**: Vercel AI SDK (@ai-sdk/*)
- **类型检查**: TypeScript
- **国际化**: i18next

### 目录结构
```
src/
├── app/                    # Next.js App Router 路由
│   ├── api/               # API 路由
│   │   ├── ai/           # AI 提供商 API 代理
│   │   ├── search/       # 搜索提供商 API
│   │   ├── sse/          # Server-Sent Events API
│   │   └── mcp/          # Model Context Protocol 服务
├── components/            # React 组件
│   ├── Research/         # 研究功能相关组件
│   ├── Knowledge/        # 知识库管理组件
│   ├── MagicDown/        # Markdown 编辑器
│   └── ui/               # shadcn/ui 组件
├── utils/                 # 工具函数
│   └── deep-research/    # 核心研究逻辑
├── hooks/                # 自定义 Hooks
├── store/                # Zustand 状态管理
├── types/                # TypeScript 类型定义
└── constants/            # 常量定义
```

### 核心研究流程
研究系统基于 `DeepResearch` 类实现，支持以下流程：

1. **研究计划生成** (`writeReportPlan()`)
2. **SERP查询生成** (`generateSERPQuery()`) 
3. **搜索任务执行** (`runSearchTask()`)
4. **最终报告生成** (`writeFinalReport()`)

### AI 模型集成
系统支持多个AI提供商，位于 `src/app/api/ai/` 目录：
- Google Gemini
- OpenAI/Azure OpenAI
- Anthropic Claude
- DeepSeek
- Mistral
- Ollama (本地)
- OpenRouter
- XAI (Grok)

### 搜索引擎集成
支持多个搜索提供商，位于 `src/app/api/search/` 目录：
- Tavily
- Firecrawl
- Exa
- SearXNG
- Bocha

### 状态管理
使用 Zustand 管理全局状态：
- `useGlobalStore`: 全局UI状态
- `useSettingStore`: 用户设置
- `useHistoryStore`: 研究历史
- `useKnowledgeStore`: 本地知识库
- `useTaskStore`: 任务管理

### 关键 Hooks
- `useDeepResearch`: 核心研究功能
- `useAutoResearch`: 自动研究功能
- `useChapterResearch`: 章节研究功能
- `useAiProvider`: AI 提供商管理
- `useWebSearch`: 搜索功能

## 技术架构集成

### 研究流程协调
Deep Research 支持三种主要研究模式的无缝切换：

#### 1. 标准流程 → 对抗性计划优化
- 用户输入研究主题后可选择启动对抗性计划优化
- 优化完成的计划可直接用于后续研究
- 支持计划导出和手动编辑

#### 2. 对抗性计划 → 章节式研究
- 优化后的计划自动解析为章节列表
- 每个章节成为独立的研究单元
- 智能体对话基于章节目标进行

#### 3. 章节式研究 → 信息收集
- 智能体达成的查询共识自动执行
- 查询结果集成到信息收集系统
- 支持多源搜索和结果聚合

### 状态管理架构

#### 全局状态 (Zustand)
- `src/store/task.ts`: 任务和对抗性计划状态
- `src/store/setting.ts`: 用户设置和偏好
- `src/store/global.ts`: 全局UI状态

#### 组件级状态
- React useState: 组件内部状态
- useCallback: 性能优化和依赖管理
- useEffect: 生命周期和副作用处理

### AI 提供商集成

#### 支持的AI服务
- OpenAI (GPT系列)
- Anthropic (Claude系列)
- Google (Gemini系列)
- 本地模型 (Ollama)
- 兼容OpenAI API的服务

#### 提供商管理
- `src/hooks/useAiProvider.ts`: AI提供商抽象层
- 统一的API调用接口
- 自动错误处理和重试机制
- 支持流式响应和实时更新

### 搜索引擎集成

#### 支持的搜索服务
- Tavily: 专业研究搜索
- Exa: 语义搜索
- SearXNG: 开源元搜索
- Firecrawl: 网页内容提取
- Bocha: 自定义搜索

#### 搜索策略
- 双语搜索支持（中英文）
- 多源结果聚合
- 智能去重和排序
- 图片和链接提取

## 章节式研究功能

### 功能概述
章节式研究是一个基于智能体对话的深度研究系统，通过Alpha和Beta两个AI智能体的协作对话来进行结构化的信息收集和分析。

### 核心文件结构

#### 管理器层
- `src/utils/deep-research/chapter-research-manager.ts`: 章节研究管理器
  - 控制整个章节研究流程
  - 管理智能体轮换和对话状态
  - 处理高层级讨论和查询规划两个阶段
  - 支持强制推进和章节结束判断

#### 提示词和类型定义
- `src/utils/deep-research/auto-research-prompts.ts`: 智能体提示词和类型定义
  - 定义Discussion、AgentAction、ConsensusResult等核心类型
  - 提供generateOpeningPrompt_v2、generateHighLevelResponsePrompt等提示词函数
  - 支持3-10个查询提议（已从2个扩展）
  - 包含章节解析和状态管理逻辑

#### 前端组件
- `src/components/Research/ChapterResearch/ChapterDialogueHistory.tsx`: 对话历史组件
  - 按轮次分组显示Alpha-Beta对话
  - 集成章节状态显示（待启动、进行中、已完成）
  - 显示进度条和阶段信息（高层级讨论/查询规划）
  - 支持原始日志查看和美观的对话展示

- `src/components/Research/ChapterResearch/ChapterConfigPanel.tsx`: 配置面板
  - 提供章节研究参数配置
  - 支持循环次数、轮数阈值等超参数调整

#### 状态管理
- `src/hooks/useChapterResearch.ts`: 章节研究Hook
  - 管理章节研究的完整生命周期
  - 处理智能体消息和状态更新
  - 提供对话历史组件的props
  - 集成章节状态跟踪（不再使用任务卡片）

### 工作流程

#### 1. 章节启动
- 解析研究计划，提取章节目标
- 初始化Alpha和Beta智能体状态
- 显示章节开始提示（集成在对话历史组件中）

#### 2. 高层级讨论阶段
- Alpha提出薄弱知识点分析
- Beta响应并达成共识或提出修正
- 支持最多n次循环，超过则强制推进

#### 3. 查询规划阶段
- 基于高层级共识制定具体查询
- 生成3-10个查询提议（根据复杂度）
- 执行查询并收集信息
- 支持最多m次循环，超过则强制推进

#### 4. 轮次管理
- 每轮包含高层级和查询两个阶段
- 达到轮数阈值后进行章节结束判断
- 支持最大轮数限制和强制结束

### 显示特性

#### 状态集成
- 章节状态（第X章/共Y章）显示在对话历史组件顶部
- 不再使用独立的任务卡片，避免信息冗余
- 进度条显示当前轮次和阶段进展

#### 对话展示
- 按轮次分组显示，每轮包含多次对话
- 支持思考过程、提议内容、查询列表等不同类型展示
- 提供原始日志和美观视图两种模式

#### 用户体验
- 对话历史组件独立显示，不在信息收集部分
- 章节开始时显示启动提示和目标说明
- 实时更新智能体状态和思考进度


## 环境配置

### 开发环境设置
1. 复制 `env.tpl` 到 `.env.local` (开发) 或 `.env` (生产)
2. 配置所需的API密钥和基础URL
3. 运行 `npm run dev` 启动开发服务器

### 关键环境变量
- `ACCESS_PASSWORD`: 服务器访问密码
- `GOOGLE_GENERATIVE_AI_API_KEY`: Gemini API密钥
- `OPENAI_API_KEY`: OpenAI API密钥
- `ANTHROPIC_API_KEY`: Claude API密钥
- `TAVILY_API_KEY`: Tavily搜索API密钥
- `NEXT_PUBLIC_BUILD_MODE`: 构建模式

## PWA 和部署

### 支持的部署平台
- Vercel (推荐)
- Cloudflare Pages
- Docker
- 静态部署

### PWA 配置
使用 Serwist 实现 PWA 功能，配置位于：
- `src/app/sw.ts`: Service Worker
- `src/app/manifest.json`: Web App Manifest

## 国际化

支持多语言：
- 中文 (zh-CN)
- 英文 (en-US) 
- 西班牙文 (es-ES)
- 越南文 (vi-VN)

语言文件位于 `src/locales/` 目录

## API 服务

### Server-Sent Events API
- 端点: `/api/sse`
- 支持实时研究进度推送
- GET/POST 两种调用方式

### Model Context Protocol (MCP)
- HTTP 端点: `/api/mcp`
- SSE 端点: `/api/mcp/sse`
- 支持作为MCP服务器运行

## 开发注意事项

### 代码风格
- 使用 TypeScript 严格模式
- 遵循 ESLint 配置规则
- 使用 shadcn/ui 组件规范

### 测试和构建
- 修改代码后运行 `npm run lint` 检查
- 构建前确保类型检查通过
- 使用 `npm run build` 验证生产构建

### 性能优化
- 启用 React Compiler (`experimental.reactCompiler: true`)
- 使用 Turbo 模式开发 (`--turbopack`)
- 支持代码分割和懒加载

### 文档和资源
- README.md: 用户使用指南
- docs/ 目录: 详细技术文档
- 支持 Docker 和 docker-compose 部署