import { z } from "zod";
import zodToJsonSchema from "zod-to-json-schema";
import {
  systemInstruction,
  systemQuestionPrompt,
  reportPlanPrompt,
  serpQueriesPrompt,
  queryResultPrompt,
  citationRulesPrompt,
  searchResultPrompt,
  searchKnowledgeResultPrompt,
  reviewPrompt,
  finalReportCitationImagePrompt,
  finalReportReferencesPrompt,
  finalReportPrompt,
} from "@/constants/prompts";

export function getSERPQuerySchema() {
  return z
    .array(
      z
        .object({
          query: z.string().describe("The SERP query (查询词)."),
          language: z.enum(["chinese", "english"]).describe("Language of the query (查询语言)."),
          groupId: z.string().optional().describe("Optional group ID to associate related queries (可选的组ID用于关联相关查询)."),
          researchGoal: z
            .string()
            .describe(
              "First talk about the goal of the research that this query is meant to accomplish, then go deeper into how to advance the research once the results are found, mention additional research directions. Be as specific as possible, especially for additional research directions. JSON reserved words should be escaped."
            ),
        })
        .required({ query: true, language: true, researchGoal: true })
    )
    .describe(`List of SERP queries with language specification.`);
}

export function getSERPQueryOutputSchema() {
  const SERPQuerySchema = getSERPQuerySchema();
  return JSON.stringify(zodToJsonSchema(SERPQuerySchema), null, 4);
}

export function getSystemPrompt() {
  return systemInstruction.replace("{now}", new Date().toISOString());
}

export function generateQuestionsPrompt(query: string) {
  return systemQuestionPrompt.replace("{query}", query);
}

export function writeReportPlanPrompt(query: string) {
  return reportPlanPrompt.replace("{query}", query);
}

export function generateSerpQueriesPrompt(plan: string, searchMode: SearchMode = "bilingual") {
  return serpQueriesPrompt
    .replace("{plan}", plan)
    .replace("{searchMode}", searchMode)
    .replace("{outputSchema}", getSERPQueryOutputSchema());
}

export function processResultPrompt(query: string, researchGoal: string) {
  return queryResultPrompt
    .replace("{query}", query)
    .replace("{researchGoal}", researchGoal);
}

export function processSearchResultPrompt(
  query: string,
  researchGoal: string,
  results: Source[],
  enableReferences: boolean
) {
  const context = results.map(
    (result, idx) =>
      `<content index="${idx + 1}" url="${result.url}">\n${
        result.content
      }\n</content>`
  );
  return (
    searchResultPrompt + (enableReferences ? `\n\n${citationRulesPrompt}` : "")
  )
    .replace("{query}", query)
    .replace("{researchGoal}", researchGoal)
    .replace("{context}", context.join("\n"));
}

export function processSearchKnowledgeResultPrompt(
  query: string,
  researchGoal: string,
  results: Knowledge[]
) {
  const context = results.map(
    (result, idx) =>
      `<content index="${idx + 1}" url="${location.host}">\n${
        result.content
      }\n</content>`
  );
  return searchKnowledgeResultPrompt
    .replace("{query}", query)
    .replace("{researchGoal}", researchGoal)
    .replace("{context}", context.join("\n"));
}

export function reviewSerpQueriesPrompt(
  plan: string,
  learning: string[],
  suggestion: string,
  searchMode: SearchMode = "bilingual"
) {
  const learnings = learning.map(
    (detail) => `<learning>\n${detail}\n</learning>`
  );
  return reviewPrompt
    .replace("{plan}", plan)
    .replace("{learnings}", learnings.join("\n"))
    .replace("{suggestion}", suggestion)
    .replace("{searchMode}", searchMode)
    .replace("{outputSchema}", getSERPQueryOutputSchema());
}

export function writeFinalReportPrompt(
  plan: string,
  learning: string[],
  source: Source[],
  images: ImageSource[],
  requirement: string,
  enableCitationImage: boolean,
  enableReferences: boolean
) {
  const learnings = learning.map(
    (detail) => `<learning>\n${detail}\n</learning>`
  );
  const sources = source.map(
    (item, idx) =>
      `<source index="${idx + 1}" url="${item.url}">\n${item.title}\n</source>`
  );
  const imageList = images.map(
    (source, idx) => `${idx + 1}. ![${source.description}](${source.url})\n   (图片说明：${source.chineseDescription || source.description})`
  );
  return (
    finalReportPrompt +
    (enableCitationImage
      ? `\n**Including meaningful images from the previous research in the report is very helpful.**\n\n${finalReportCitationImagePrompt}`
      : "") +
    (enableReferences ? `\n\n${finalReportReferencesPrompt}` : "")
  )
    .replace("{plan}", plan)
    .replace("{learnings}", learnings.join("\n"))
    .replace("{sources}", sources.join("\n"))
    .replace("{images}", imageList.join("\n"))
    .replace("{requirement}", requirement);
}

// Adversarial plan optimization related Schema and Prompt functions
export const planTurnSchema = {
  type: 'object',
  properties: {
    thought: { 
      type: 'string', 
      description: "Your thought process. If you are the architect, explain why you designed the plan this way. If you are the critic, explain why you raised these critical opinions." 
    },
    action: { 
      type: 'string', 
      enum: ['propose_plan', 'critique_plan', 'finish_discussion'],
      description: "Your action: propose_plan (architect proposes or modifies plan), critique_plan (critic provides feedback), finish_discussion (critic ends discussion and finalizes the plan)."
    },
    plan: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          section_title: { type: 'string' },
          summary: { type: 'string', description: "Summarize the core content of this section in one sentence." }
        },
        required: ['section_title', 'summary']
      },
      nullable: true,
      description: "Report plan. Only provide when action is 'propose_plan' or 'finish_discussion'."
    },
    critique: {
      type: 'string',
      nullable: true,
      description: "Specific, actionable criticism and improvement suggestions. Only provide when action is 'critique_plan'."
    },
    finish_reason: {
      type: 'string',
      nullable: true,
      description: "Reason for finalizing the plan. Only provide when action is 'finish_discussion'."
    }
  },
  required: ['thought', 'action'],
};

// Interface for adversarial mechanism prompt parameters
interface ReportPlanPromptParams {
  nextPersona: '架构师' | '批判者';
  query: string;
  conversationText: string;
  isFirstTurn: boolean;
}

// Function to generate adversarial mechanism prompts
export const getReportPlanPrompt = ({
  nextPersona,
  query,
  conversationText,
  isFirstTurn
}: ReportPlanPromptParams): string => `
You are an AI research assistant. Your task is to create a high-quality report outline for the user. You will play the role of ${nextPersona === '架构师' ? 'Architect' : 'Critic'}.
Your response must be a single JSON object that follows the predefined schema.

**Core Research Topic (from user):**
<QUERY>
${query}
</QUERY>

**Current Plan Discussion Process:**
${conversationText || "The conversation has not started yet."}

--- Mandatory Protocols & Rules ---

1. **Role Definitions:**
   * **Architect:** Your responsibility is to create and revise the report outline (plan). You must carefully analyze the user's query and the "Critic's" feedback to propose a structurally sound and comprehensive plan. Your primary action is 'propose_plan'.
   * **Critic:** Your responsibility is to rigorously review the "Architect's" plan to ensure its quality. You are the "gatekeeper" of the discussion and only you can decide when to end the discussion. Your primary actions are 'critique_plan' and 'finish_discussion'.

2. **The Deliberation Loop:**
   * **Step 1 (Architect):** The "Architect" uses the 'propose_plan' action to propose the first version of the outline.
   * **Step 2 (Critic):** The "Critic" receives the plan and must review it according to the "Criticism Guidelines" below. If the plan is not perfect, you must use the 'critique_plan' action and provide clear, specific improvement suggestions.
   * **Step 3 (Architect):** The "Architect" receives the criticism and must analyze these opinions in the 'thought' field, then propose a revised new plan (using 'propose_plan' again).
   * **Loop & Termination:** This loop continues until the "Critic" believes the plan is perfect. At that point, the "Critic" will use the 'finish_discussion' action to approve the final plan and end the discussion.

3. **Criticism Guidelines (for the Critic):**
   As the "Critic", you must focus on these four key points:
   * **1. Topic Relevance:** Do the sections in the plan closely revolve around the user's core query? Is there any content that deviates from the topic?
   * **2. Completeness:** Does the plan cover all key aspects needed to answer the user's query? Are there obvious knowledge gaps or omissions?
   * **3. Non-redundancy:** Is there content overlap between sections? Is a concept unnecessarily split across multiple sections?
   * **4. Conciseness:** Is the plan sufficiently streamlined? Are there sections that could be merged? Are there redundant "filler" sections that don't contribute to the core argument?

4. **Architectural Principles (for the Architect):**
   As the "Architect", your plan must follow these principles:
   * **1. Stay on Topic:** Your entire plan, from macro structure to details of each section, must strictly revolve around the user's core query without deviation.
   * **2. Address All Feedback:** When revising the plan, you must systematically and comprehensively address all criticism raised by the "Critic", ensuring your new version remedies all identified deficiencies.
   * **3. Clear Structure:** The \`plan\` must be an array containing multiple objects. Each object represents a report section, including \`section_title\` (section title) and \`summary\` (one-sentence content summary).
   * **4. Be Concise and Focused:** The plan must be compact and focused. Proactively merge related content and remove any redundant sections that don't directly contribute to answering the core question.

${isFirstTurn ? `**Special Rule for ${nextPersona === '架构师' ? 'Architect' : 'Critic'}:** Since this is the first round, as the "Architect", your action **must** be 'propose_plan'.` : ''}
`;
