import { z } from "zod";
import zodToJsonSchema from "zod-to-json-schema";
import {
  systemInstruction,
  systemQuestionPrompt,
  reportPlanPrompt,
  serpQueriesPrompt,
  queryResultPrompt,
  citationRulesPrompt,
  searchResultPrompt,
  searchKnowledgeResultPrompt,
  reviewPrompt,
  finalReportCitationImagePrompt,
  finalReportReferencesPrompt,
  finalReportPrompt,
} from "@/constants/prompts";

export function getSERPQuerySchema() {
  return z
    .array(
      z
        .object({
          query: z.string().describe("The SERP query (查询词)."),
          language: z.enum(["chinese", "english"]).describe("Language of the query (查询语言)."),
          groupId: z.string().optional().describe("Optional group ID to associate related queries (可选的组ID用于关联相关查询)."),
          researchGoal: z
            .string()
            .describe(
              "First talk about the goal of the research that this query is meant to accomplish, then go deeper into how to advance the research once the results are found, mention additional research directions. Be as specific as possible, especially for additional research directions. JSON reserved words should be escaped."
            ),
        })
        .required({ query: true, language: true, researchGoal: true })
    )
    .describe(`List of SERP queries with language specification.`);
}

export function getSERPQueryOutputSchema() {
  const SERPQuerySchema = getSERPQuerySchema();
  return JSON.stringify(zodToJsonSchema(SERPQuerySchema), null, 4);
}

export function getSystemPrompt() {
  return systemInstruction.replace("{now}", new Date().toISOString());
}

export function generateQuestionsPrompt(query: string) {
  return systemQuestionPrompt.replace("{query}", query);
}

export function writeReportPlanPrompt(query: string) {
  return reportPlanPrompt.replace("{query}", query);
}

export function generateSerpQueriesPrompt(plan: string, searchMode: SearchMode = "bilingual") {
  return serpQueriesPrompt
    .replace("{plan}", plan)
    .replace("{searchMode}", searchMode)
    .replace("{outputSchema}", getSERPQueryOutputSchema());
}

export function processResultPrompt(query: string, researchGoal: string) {
  return queryResultPrompt
    .replace("{query}", query)
    .replace("{researchGoal}", researchGoal);
}

export function processSearchResultPrompt(
  query: string,
  researchGoal: string,
  results: Source[],
  enableReferences: boolean
) {
  const context = results.map(
    (result, idx) =>
      `<content index="${idx + 1}" url="${result.url}">\n${
        result.content
      }\n</content>`
  );
  return (
    searchResultPrompt + (enableReferences ? `\n\n${citationRulesPrompt}` : "")
  )
    .replace("{query}", query)
    .replace("{researchGoal}", researchGoal)
    .replace("{context}", context.join("\n"));
}

export function processSearchKnowledgeResultPrompt(
  query: string,
  researchGoal: string,
  results: Knowledge[]
) {
  const context = results.map(
    (result, idx) =>
      `<content index="${idx + 1}" url="${location.host}">\n${
        result.content
      }\n</content>`
  );
  return searchKnowledgeResultPrompt
    .replace("{query}", query)
    .replace("{researchGoal}", researchGoal)
    .replace("{context}", context.join("\n"));
}

export function reviewSerpQueriesPrompt(
  plan: string,
  learning: string[],
  suggestion: string,
  searchMode: SearchMode = "bilingual"
) {
  const learnings = learning.map(
    (detail) => `<learning>\n${detail}\n</learning>`
  );
  return reviewPrompt
    .replace("{plan}", plan)
    .replace("{learnings}", learnings.join("\n"))
    .replace("{suggestion}", suggestion)
    .replace("{searchMode}", searchMode)
    .replace("{outputSchema}", getSERPQueryOutputSchema());
}

export function writeFinalReportPrompt(
  plan: string,
  learning: string[],
  source: Source[],
  images: ImageSource[],
  requirement: string,
  enableCitationImage: boolean,
  enableReferences: boolean
) {
  const learnings = learning.map(
    (detail) => `<learning>\n${detail}\n</learning>`
  );
  const sources = source.map(
    (item, idx) =>
      `<source index="${idx + 1}" url="${item.url}">\n${item.title}\n</source>`
  );
  const imageList = images.map(
    (source, idx) => `${idx + 1}. ![${source.description}](${source.url})\n   (图片说明：${source.chineseDescription || source.description})`
  );
  return (
    finalReportPrompt +
    (enableCitationImage
      ? `\n**Including meaningful images from the previous research in the report is very helpful.**\n\n${finalReportCitationImagePrompt}`
      : "") +
    (enableReferences ? `\n\n${finalReportReferencesPrompt}` : "")
  )
    .replace("{plan}", plan)
    .replace("{learnings}", learnings.join("\n"))
    .replace("{sources}", sources.join("\n"))
    .replace("{images}", imageList.join("\n"))
    .replace("{requirement}", requirement);
}

// Adversarial plan optimization related Schema and Prompt functions
export const planTurnSchema = {
  type: 'object',
  properties: {
    thought: { 
      type: 'string', 
      description: "Your thought process. If you are the architect, explain why you designed the plan this way. If you are the critic, explain why you raised these critical opinions." 
    },
    action: { 
      type: 'string', 
      enum: ['propose_plan', 'critique_plan', 'finish_discussion'],
      description: "Your action: propose_plan (architect proposes or modifies plan), critique_plan (critic provides feedback), finish_discussion (critic ends discussion and finalizes the plan)."
    },
    plan: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          section_title: { type: 'string' },
          summary: { type: 'string', description: "Summarize the core content of this section in one sentence." }
        },
        required: ['section_title', 'summary']
      },
      nullable: true,
      description: "Report plan. Only provide when action is 'propose_plan' or 'finish_discussion'."
    },
    critique: {
      oneOf: [
        { type: 'string' },
        {
          type: 'object',
          properties: {
            topic_relevance: {
              type: 'array',
              items: { type: 'string' }
            },
            completeness: {
              type: 'array', 
              items: { type: 'string' }
            },
            non_redundancy: {
              type: 'array',
              items: { type: 'string' }
            },
            conciseness: {
              type: 'array',
              items: { type: 'string' }
            },
            abstraction_guidance: {
              type: 'array',
              items: { type: 'string' }
            },
            logical_flow: {
              type: 'array',
              items: { type: 'string' }
            }
          }
        }
      ],
      nullable: true,
      description: "REQUIRED when action is 'critique_plan': Specific, actionable criticism and improvement suggestions. Can be either a string of feedback or a structured object with categorized critiques. MUST be null/empty for other actions."
    },
    finish_reason: {
      type: 'string',
      nullable: true,
      description: "Reason for finalizing the plan. Only provide when action is 'finish_discussion'."
    }
  },
  required: ['thought', 'action'],
  additionalProperties: false,
};

// Interface for adversarial mechanism prompt parameters
interface ReportPlanPromptParams {
  nextPersona: '架构师' | '批判者';
  query: string;
  conversationText: string;
  isFirstTurn: boolean;
  mode?: 'fixed' | 'auto';
  maxRounds?: number;
  currentRound?: number;
}

// Function to generate adversarial mechanism prompts
export const getReportPlanPrompt = ({
  nextPersona,
  query,
  conversationText,
  isFirstTurn,
  mode = 'auto',
  maxRounds = 5,
  currentRound = 1
}: ReportPlanPromptParams): string => `
You are an AI research assistant. Your task is to create a high-quality report outline for the user. You will play the role of ${nextPersona === '架构师' ? 'Architect' : 'Critic'}.
Your response must be a single JSON object that follows the predefined schema.

**Core Research Topic (from user):**
<QUERY>
${query}
</QUERY>

**Current Plan Discussion Process:**
${conversationText || "The conversation has not started yet."}

--- Mandatory Protocols & Rules ---

1. **Role Definitions:**
   * **Architect:** Your responsibility is to create and revise the report outline (plan). You must carefully analyze the user's query and the "Critic's" feedback to propose a structurally sound and comprehensive plan. Your primary action is 'propose_plan'.
   * **Critic:** Your responsibility is to rigorously review the "Architect's" plan to ensure its quality. You are the "gatekeeper" of the discussion and only you can decide when to end the discussion. Your primary actions are 'critique_plan' and 'finish_discussion'. You must ensure that every piece of feedback you provide is necessary and essential—prioritize quality over quantity in your critiques.

2. **Critical Output Requirements:**
   * **thought field:** Use this for your internal reasoning process - explain WHY you're taking this action and your analysis of the current situation.
   * **critique field:** ONLY for Critic when action is 'critique_plan' - provide SPECIFIC, ACTIONABLE improvement suggestions that the Architect can directly implement. DO NOT put critique content in thought field.
   * **plan field:** ONLY for Architect when action is 'propose_plan' or Critic when action is 'finish_discussion' - the actual report outline structure.
   * **finish_reason field:** ONLY for Critic when action is 'finish_discussion' - explain why the plan is now satisfactory.

3. **The Deliberation Loop:**
   * **Step 1 (Architect):** The "Architect" uses the 'propose_plan' action to propose the first version of the outline.
   * **Step 2 (Critic):** The "Critic" receives the plan and must review it according to the "Criticism Guidelines" below. If the plan is not perfect, you must use the 'critique_plan' action and provide clear, specific improvement suggestions.
   * **Step 3 (Architect):** The "Architect" receives the criticism and must analyze these opinions in the 'thought' field, then propose a revised new plan (using 'propose_plan' again).
   * **Loop & Termination:** This loop continues until the "Critic" believes the plan is perfect. At that point, the "Critic" will use the 'finish_discussion' action to approve the final plan and end the discussion.

4. **Criticism Guidelines (for the Critic):**
   As the "Critic", you must focus on these four key points:
   * **1. Topic Relevance:** Do the sections in the plan closely revolve around the user's core query? Is there any content that deviates from the topic?
   * **2. Completeness:** Does the plan cover all key aspects needed to answer the user's query? Are there obvious knowledge gaps or omissions?
   * **3. Non-redundancy (Content-level):** Is there content overlap between sections? (i.e., ensure the same topic is not discussed in multiple sections).
   * **4. Conciseness (Structural-level): Is the plan structurally streamlined? (i.e., merge overly granular sections or remove unnecessary 'filler' sections).
   * **5. Abstraction & Guidance:**Does the plan provide a flexible research framework, or is it overly prescriptive with specific, potentially outdated examples? The section summaries must define the purpose and scope of the research for that section, not pre-populate it with factual content. For example, 'Analyze key industry case studies' is better than 'Analyze the case studies of Company A and Company B'.
   * **6. Logical Flow:** Is the transition between sections natural? Is the overall argument structure of the outline progressive and logical?

5. **Architectural Principles (for the Architect):**
   As the "Architect", your plan must follow these principles:
   * **1. Stay on Topic:** Your entire plan, from macro structure to details of each section, must strictly revolve around the user's core query without deviation.
   * **2. Clear Structure:** The \`plan\` must be an array containing multiple objects. Each object represents a report section, including \`section_title\` (section title) and \`summary\`. The \`summary\` must describe the core question the section answers or its primary research objective, rather than listing specific facts or examples. It should guide the user on what to investigate, not what has already been found.
   * **3. Be Concise and Focused:** The plan must be compact and focused. Proactively merge related content and remove any redundant sections that don't directly contribute to answering the core question.
   * **4. Abstract Framework:** The outline must serve as a research scaffold, not a pre-written summary. Avoid mentioning specific, non-foundational examples, models, or case studies. Instead, describe the types of examples or evidence the user should seek (e.g., 'Examine seminal models' instead of 'Examine BERT and GPT-2').
   * **5. Address All Feedback:** Systematically review and address all feedback from the "Critic". While you should aim to remedy all identified deficiencies, if a piece of feedback directly conflicts with other core principles (especially #2 'Abstract Framework'), you must prioritize the core principles. In this case, you must explain your reasoning for not adopting the specific feedback in the 'thought' field.

${isFirstTurn ? `**Special Rule for ${nextPersona === '架构师' ? 'Architect' : 'Critic'}:** Since this is the first round, as the "Architect", your action **must** be 'propose_plan'.` : ''}

${mode === 'fixed' ? `**FIXED ROUNDS MODE RULES:**
- This discussion is set to run for EXACTLY ${maxRounds} rounds (current round: ${currentRound}/${maxRounds})
- As the ARCHITECT, you can ONLY use 'propose_plan' action (never 'finish_discussion')
- As the CRITIC, you MUST NOT use 'finish_discussion' action until round ${maxRounds}
- Continue the discussion until the final round
- Only the CRITIC can use 'finish_discussion' and only when currentRound equals ${maxRounds}` : ''}

**CRITICAL REMINDERS:**
- If you are the CRITIC and use action 'critique_plan', you MUST fill the critique field with specific, actionable feedback. DO NOT leave it empty or put critique content only in thought field.
- If you are the ARCHITECT and use action 'propose_plan', you MUST fill the plan field and leave critique field empty.
- Your response must be valid JSON that exactly matches the required schema
- Your response must use Chinese language.
`;
