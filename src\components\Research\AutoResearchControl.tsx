"use client";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Play,
  Square,
  ChevronDown,
  ChevronUp,
  LoaderCircle,
} from "lucide-react";
import { Button } from "@/components/Internal/Button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
// 移除未使用的导入
import { Badge } from "@/components/ui/badge";
import { useAutoResearch } from "@/hooks/useAutoResearch";
import { useTaskStore } from "@/store/task";
import { formatRoundLabel } from "@/types/auto-research";

/**
 * 自动研究控制面板组件
 * 提供自动研究的配置、启动、停止和进度显示功能
 */
function AutoResearchControl() {
  const { t } = useTranslation();
  const taskStore = useTaskStore();
  const {
    // 状态
    isProcessing,
    currentStatus,
    isRunning,
    currentRound,
    config,
    showConfig,

    // 操作函数
    startAutoResearch,
    stopAutoResearch,

    // 章节研究功能已集成到主要函数中

    // 配置函数
    setShowConfig,
  } = useAutoResearch();

  // 章节研究现在是默认且唯一的模式

  // 配置状态已简化，章节研究不需要复杂配置

  // 检查是否有报告计划（自动研究的前提条件）
  const hasReportPlan = Boolean(taskStore.reportPlan);

  // 调试信息
  console.log("🔍 AutoResearchControl 渲染状态:");
  console.log("📊 isRunning:", isRunning);
  console.log("📊 showConfig:", showConfig);
  console.log("📊 hasReportPlan:", hasReportPlan);
  console.log("📊 isProcessing:", isProcessing);
  console.log("📊 按钮禁用状态:", isProcessing || !hasReportPlan);
  console.log("📊 taskStore.autoResearch.runtime.isRunning:", taskStore.autoResearch.runtime.isRunning);

  // 监控showConfig变化
  useEffect(() => {
    console.log("🔄 showConfig 状态变化:", showConfig);
  }, [showConfig]);

  /**
   * 更新配置并同步到store
   */
  // 配置处理已简化，章节研究不需要复杂配置

  /**
   * 切换配置面板显示
   */
  const toggleConfigPanel = () => {
    console.log("⚙️ toggleConfigPanel 被调用");
    console.log("📊 当前 showConfig:", showConfig);
    setShowConfig(!showConfig);
    console.log("📊 新的 showConfig:", !showConfig);
  };

  /**
   * 启动自动研究（现在默认使用章节研究模式）
   */
  const handleStartAutoResearch = async () => {
    console.log("🎯 handleStartAutoResearch 被调用");
    console.log("📋 hasReportPlan:", hasReportPlan);

    if (!hasReportPlan) {
      console.log("❌ 没有报告计划，退出");
      return;
    }

    console.log("🚀 调用 startAutoResearch");
    // 直接启动章节研究
    await startAutoResearch();
  };

  /**
   * 停止自动研究
   */
  const handleStopAutoResearch = () => {
    stopAutoResearch();
  };

  /**
   * 计算进度百分比
   */
  const getProgressPercentage = () => {
    if (!isRunning || config.maxRounds === 0) return 0;
    return Math.min((currentRound / config.maxRounds) * 100, 100);
  };

  // 如果没有报告计划，不显示组件
  if (!hasReportPlan) {
    return null;
  }

  return (
    <Card className="mt-4 print:hidden">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            📖
            章节式研究
          </CardTitle>
          
          {/* 主控制按钮 */}
          <div className="flex items-center gap-2">
            {isRunning ? (
              <Button
                variant="destructive"
                size="sm"
                onClick={handleStopAutoResearch}
                disabled={isProcessing}
              >
                <Square className="h-4 w-4 mr-1" />
                {t("research.autoResearch.stop", "停止研究")}
              </Button>
            ) : (
              <Button
                variant="default"
                size="sm"
                onClick={handleStartAutoResearch}
                disabled={isProcessing || !hasReportPlan}
              >
                <>
                  <Play className="h-4 w-4 mr-1" />
                  📖 章节研究
                </>
              </Button>
            )}
            
            {/* 配置面板切换按钮 */}
            {!isRunning && (
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleConfigPanel}
              >
                {showConfig ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* 运行状态显示 */}
        {isRunning && (
          <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <LoaderCircle className="h-4 w-4 animate-spin text-blue-600" />
                <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  {currentStatus || t("research.autoResearch.running", "自动研究进行中")}
                </span>
              </div>
              <Badge variant="secondary">
                {formatRoundLabel(currentRound)}
              </Badge>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{t("research.autoResearch.progress", "进度")}</span>
                <span>{currentRound} / {config.maxRounds}</span>
              </div>
              <Progress value={getProgressPercentage()} className="h-2" />
            </div>
          </div>
        )}

        {/* 配置面板 */}
        {showConfig && !isRunning && (
          <div className="space-y-4">
            <Separator />

            {/* 章节研究说明 */}
            <div className="text-xs text-muted-foreground space-y-1 p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
              <p>• 📖 基于研究计划自动解析章节结构</p>
              <p>• 🤖 Alpha和Beta智能体进行对称辩论</p>
              <p>• 🔄 每章节独立上下文，避免信息过载</p>
              <p>• ✅ 通过共识机制确保研究质量</p>
            </div>

          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default AutoResearchControl;
