"use client";
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Settings,
  Play,
  Square,
  ChevronDown,
  ChevronUp,
  LoaderCircle,
  Target,
  BarChart3,
  Timer
} from "lucide-react";
import { Button } from "@/components/Internal/Button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { useAutoResearch } from "@/hooks/useAutoResearch";
import { useTaskStore } from "@/store/task";
import { formatRoundLabel } from "@/types/auto-research";

/**
 * 自动研究控制面板组件
 * 提供自动研究的配置、启动、停止和进度显示功能
 */
function AutoResearchControl() {
  const { t } = useTranslation();
  const taskStore = useTaskStore();
  const {
    // 状态
    isProcessing,
    currentStatus,
    isRunning,
    currentRound,
    config,
    showConfig,
    
    // 操作函数
    startAutoResearch,
    stopAutoResearch,
    
    // 配置函数
    updateConfig,
    setShowConfig,
  } = useAutoResearch();

  // 本地状态
  const [localConfig, setLocalConfig] = useState(config);

  // 同步配置状态
  useEffect(() => {
    setLocalConfig(config);
  }, [config]);

  // 检查是否有报告计划（自动研究的前提条件）
  const hasReportPlan = Boolean(taskStore.reportPlan);

  /**
   * 更新配置并同步到store
   */
  const handleConfigChange = (key: keyof typeof config, value: number | boolean) => {
    const newConfig = { ...localConfig, [key]: value };
    setLocalConfig(newConfig);
    updateConfig({ [key]: value });
  };

  /**
   * 切换配置面板显示
   */
  const toggleConfigPanel = () => {
    setShowConfig(!showConfig);
  };

  /**
   * 启动自动研究
   */
  const handleStartAutoResearch = async () => {
    if (!hasReportPlan) {
      return;
    }
    await startAutoResearch();
  };

  /**
   * 停止自动研究
   */
  const handleStopAutoResearch = () => {
    stopAutoResearch();
  };

  /**
   * 计算进度百分比
   */
  const getProgressPercentage = () => {
    if (!isRunning || config.maxRounds === 0) return 0;
    return Math.min((currentRound / config.maxRounds) * 100, 100);
  };

  // 如果没有报告计划，不显示组件
  if (!hasReportPlan) {
    return null;
  }

  return (
    <Card className="mt-4 print:hidden">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            {t("research.autoResearch.title", "自动研究")}
          </CardTitle>
          
          {/* 主控制按钮 */}
          <div className="flex items-center gap-2">
            {isRunning ? (
              <Button
                variant="destructive"
                size="sm"
                onClick={handleStopAutoResearch}
                disabled={isProcessing}
              >
                <Square className="h-4 w-4 mr-1" />
                {t("research.autoResearch.stop", "停止研究")}
              </Button>
            ) : (
              <Button
                variant="default"
                size="sm"
                onClick={showConfig ? handleStartAutoResearch : toggleConfigPanel}
                disabled={isProcessing || !hasReportPlan}
              >
                {showConfig ? (
                  <>
                    <Play className="h-4 w-4 mr-1" />
                    {t("research.autoResearch.start", "开始自动研究")}
                  </>
                ) : (
                  <>
                    <Settings className="h-4 w-4 mr-1" />
                    {t("research.autoResearch.configure", "开始自动研究")}
                  </>
                )}
              </Button>
            )}
            
            {/* 配置面板切换按钮 */}
            {!isRunning && (
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleConfigPanel}
              >
                {showConfig ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* 运行状态显示 */}
        {isRunning && (
          <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <LoaderCircle className="h-4 w-4 animate-spin text-blue-600" />
                <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  {currentStatus || t("research.autoResearch.running", "自动研究进行中")}
                </span>
              </div>
              <Badge variant="secondary">
                {formatRoundLabel(currentRound)}
              </Badge>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{t("research.autoResearch.progress", "进度")}</span>
                <span>{currentRound} / {config.maxRounds}</span>
              </div>
              <Progress value={getProgressPercentage()} className="h-2" />
            </div>
          </div>
        )}

        {/* 配置面板 */}
        {showConfig && !isRunning && (
          <div className="space-y-4">
            <Separator />
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* 搜集轮数 */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                  <Label className="text-sm font-medium">
                    {t("research.autoResearch.maxRounds", "搜集轮数")}
                  </Label>
                </div>
                <div className="px-2">
                  <Slider
                    value={[localConfig.maxRounds]}
                    onValueChange={([value]) => handleConfigChange('maxRounds', value)}
                    max={10}
                    min={1}
                    step={1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>1</span>
                    <span className="font-medium">{localConfig.maxRounds === 10 ? '∞' : localConfig.maxRounds}</span>
                    <span>∞</span>
                  </div>
                </div>
              </div>

              {/* 初始查询数 */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-muted-foreground" />
                  <Label className="text-sm font-medium">
                    {t("research.autoResearch.initialQueries", "初始查询数")}
                  </Label>
                </div>
                <div className="px-2">
                  <Slider
                    value={[localConfig.initialQueries]}
                    onValueChange={([value]) => handleConfigChange('initialQueries', value)}
                    max={10}
                    min={1}
                    step={1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>1</span>
                    <span className="font-medium">{localConfig.initialQueries}</span>
                    <span>10</span>
                  </div>
                </div>
              </div>

              {/* 报告完整性 */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                  <Label className="text-sm font-medium">
                    {t("research.autoResearch.qualityThreshold", "报告完整性")}
                  </Label>
                </div>
                <div className="px-2">
                  <Slider
                    value={[localConfig.qualityThreshold]}
                    onValueChange={([value]) => handleConfigChange('qualityThreshold', value)}
                    max={1}
                    min={0.05}
                    step={0.05}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>5%</span>
                    <span className="font-medium">{(localConfig.qualityThreshold * 100).toFixed(0)}%</span>
                    <span>100%</span>
                  </div>
                </div>
              </div>

              {/* 最长时间 */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Timer className="h-4 w-4 text-muted-foreground" />
                  <Label className="text-sm font-medium">
                    {t("research.autoResearch.maxDuration", "最长时间(分钟)")}
                  </Label>
                </div>
                <div className="px-2">
                  <Slider
                    value={[localConfig.maxDuration]}
                    onValueChange={([value]) => handleConfigChange('maxDuration', value)}
                    max={30}
                    min={2}
                    step={2}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>2</span>
                    <span className="font-medium">{localConfig.maxDuration === 30 ? '∞' : localConfig.maxDuration}</span>
                    <span>∞</span>
                  </div>
                </div>
              </div>
            </div>

            <Separator />
            
            {/* 配置说明 */}
            <div className="text-xs text-muted-foreground space-y-1">
              <p>• {t("research.autoResearch.configTip1", "自动研究将基于当前报告计划进行多轮深入研究")}</p>
              <p>• {t("research.autoResearch.configTip2", "每轮包含搜索、反思分析和质量评估")}</p>
              <p>• {t("research.autoResearch.configTip3", "达到质量阈值或时间限制时将自动停止")}</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default AutoResearchControl;
