"use client";
import { useState } from "react";
import { 
  MessageSquare, 
  ChevronDown, 
  ChevronRight, 
  Brain, 
  Search,
  Lightbulb,
  CheckCircle,
  Zap
} from "lucide-react";
import { Button } from "@/components/Internal/Button";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Discussion, AgentAction } from "@/utils/deep-research/auto-research-prompts";

interface ChapterDebateViewProps {
  chapterId: string;
  chapterGoal: string;
  discussions: Discussion[];
  currentRound: number;
  isActive: boolean;
  isRunning: boolean;
  currentAgent?: 'Alpha' | 'Beta';
  onClose?: () => void;

  // 新增状态机相关属性
  currentPhase?: 'HIGH_LEVEL' | 'QUERY';
  highLevelLoopCount?: number;
  queryLoopCount?: number;
  maxHighLevelLoops?: number;
  maxQueryLoops?: number;
  roundThreshold?: number;
  maxRounds?: number;
}

export default function ChapterDebateView({
  chapterGoal,
  discussions,
  currentRound,
  isActive,
  isRunning,
  currentAgent,
  currentPhase = 'HIGH_LEVEL',
  highLevelLoopCount = 0,
  queryLoopCount = 0,
  maxHighLevelLoops = 3,
  maxQueryLoops = 3,
  roundThreshold = 2,
  maxRounds = 5
}: ChapterDebateViewProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [showRawLog, setShowRawLog] = useState(false);

  // 获取动作图标
  const getActionIcon = (actionName: string) => {
    switch (actionName) {
      case 'HIGH_LEVEL_PROPOSE':
        return <Lightbulb className="h-4 w-4" />;
      case 'HIGH_LEVEL_AGREE':
        return <CheckCircle className="h-4 w-4" />;
      case 'QUERY_PROPOSE':
        return <Search className="h-4 w-4" />;
      case 'EXECUTE_QUERIES':
        return <Zap className="h-4 w-4" />;
      case 'CHAPTER_END_PROPOSE':
        return <MessageSquare className="h-4 w-4" />;
      case 'CHAPTER_END_AGREE':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <MessageSquare className="h-4 w-4" />;
    }
  };

  // 获取智能体图标
  const getAgentIcon = (agentId: 'Alpha' | 'Beta') => {
    return agentId === 'Alpha' ? '🤖' : '🔍';
  };

  // 获取动作颜色
  const getActionColor = (actionName: string) => {
    switch (actionName) {
      case 'HIGH_LEVEL_PROPOSE':
        return 'bg-blue-100 text-blue-800';
      case 'HIGH_LEVEL_AGREE':
        return 'bg-green-100 text-green-800';
      case 'QUERY_PROPOSE':
        return 'bg-yellow-100 text-yellow-800';
      case 'EXECUTE_QUERIES':
        return 'bg-purple-100 text-purple-800';
      case 'CHAPTER_END_PROPOSE':
        return 'bg-red-100 text-red-800';
      case 'CHAPTER_END_AGREE':
        return 'bg-emerald-100 text-emerald-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取智能体颜色
  const getAgentColor = (agentId: 'Alpha' | 'Beta') => {
    return agentId === 'Alpha' 
      ? 'bg-blue-50 text-blue-700 border-blue-200' 
      : 'bg-orange-50 text-orange-700 border-orange-200';
  };

  // 渲染收缩状态
  const renderCollapsedState = () => {
    if (!isActive) {
      return <Badge variant="secondary">待启动</Badge>;
    }
    if (isRunning) {
      return <Badge variant="default">第{currentRound}轮 - {currentAgent}思考中</Badge>;
    }
    if (discussions.length > 0) {
      return <Badge variant="default" className="bg-green-100 text-green-800">✅ 讨论完成</Badge>;
    }
    return null;
  };

  // 获取进度
  const getProgress = () => {
    if (!isActive) return 0;
    // 基于轮数和最大轮数计算进度
    return Math.min((currentRound / maxRounds) * 100, 100);
  };

  // 获取阶段显示信息
  const getPhaseInfo = () => {
    if (currentPhase === 'HIGH_LEVEL') {
      return {
        name: '高层级讨论',
        icon: '💡',
        color: 'bg-blue-100 text-blue-800',
        progress: highLevelLoopCount / maxHighLevelLoops,
        count: `${highLevelLoopCount}/${maxHighLevelLoops}`
      };
    } else {
      return {
        name: '查询规划',
        icon: '🔍',
        color: 'bg-purple-100 text-purple-800',
        progress: queryLoopCount / maxQueryLoops,
        count: `${queryLoopCount}/${maxQueryLoops}`
      };
    }
  };

  // 检查是否接近强制推进
  const isNearForced = () => {
    if (currentPhase === 'HIGH_LEVEL') {
      return highLevelLoopCount >= maxHighLevelLoops - 1;
    } else {
      return queryLoopCount >= maxQueryLoops - 1;
    }
  };

  // 检查是否接近章节结束
  const isNearChapterEnd = () => {
    return currentRound >= roundThreshold;
  };

  // 渲染payload内容
  const renderPayloadContent = (action: AgentAction) => {
    const { actionName, payload } = action;
    
    if (actionName === 'EXECUTE_QUERIES' && payload.queries) {
      return (
        <div className="border-l-2 pl-4 border-purple-300 mt-2 pt-2">
          <p className="font-medium text-purple-600 text-xs uppercase tracking-wider">执行查询</p>
          <ul className="mt-2 space-y-1 list-disc list-inside text-gray-800">
            {payload.queries.map((query, i) => (
              <li key={i}>
                <span className="font-semibold">{query.query}</span>
                <span className="text-sm text-gray-600"> - {query.researchGoal}</span>
              </li>
            ))}
          </ul>
        </div>
      );
    }

    if (actionName === 'QUERY_PROPOSE' && payload.queries) {
      return (
        <div className="border-l-2 pl-4 border-yellow-300 mt-2 pt-2">
          <p className="font-medium text-yellow-600 text-xs uppercase tracking-wider">提议查询</p>
          <ul className="mt-2 space-y-1 list-disc list-inside text-gray-800">
            {payload.queries.map((query, i) => (
              <li key={i}>
                <span className="font-semibold">{query.query}</span>
                <span className="text-sm text-gray-600"> - {query.researchGoal}</span>
              </li>
            ))}
          </ul>
        </div>
      );
    }

    if (actionName === 'HIGH_LEVEL_PROPOSE' && payload.proposal) {
      return (
        <div className="border-l-2 pl-4 border-blue-300 mt-2 pt-2">
          <p className="font-medium text-blue-600 text-xs uppercase tracking-wider">高层级提议</p>
          <p className="mt-1 text-gray-800">{payload.proposal}</p>
        </div>
      );
    }

    if (actionName === 'HIGH_LEVEL_AGREE' && payload.agreement) {
      return (
        <div className="border-l-2 pl-4 border-green-300 mt-2 pt-2">
          <p className="font-medium text-green-600 text-xs uppercase tracking-wider">高层级赞同</p>
          <p className="mt-1 text-gray-800">{payload.agreement}</p>
        </div>
      );
    }

    if (actionName === 'CHAPTER_END_PROPOSE' && payload.justification) {
      return (
        <div className="border-l-2 pl-4 border-red-300 mt-2 pt-2">
          <p className="font-medium text-red-600 text-xs uppercase tracking-wider">章节结束提议</p>
          <p className="mt-1 text-gray-800">{payload.justification}</p>
        </div>
      );
    }

    if (actionName === 'CHAPTER_END_AGREE' && payload.justification) {
      return (
        <div className="border-l-2 pl-4 border-emerald-300 mt-2 pt-2">
          <p className="font-medium text-emerald-600 text-xs uppercase tracking-wider">章节结束赞同</p>
          <p className="mt-1 text-gray-800">{payload.justification}</p>
        </div>
      );
    }

    if (payload.rationale) {
      return (
        <div className="border-l-2 pl-4 border-gray-300 mt-2 pt-2">
          <p className="font-medium text-gray-600 text-xs uppercase tracking-wider">理由说明</p>
          <p className="mt-1 text-gray-800">{payload.rationale}</p>
        </div>
      );
    }

    return null;
  };

  return (
    <Card className="w-full border-dashed border-2 p-4">
      <CardHeader className="cursor-pointer" onClick={() => setIsExpanded(!isExpanded)}>
        <div className="flex justify-between items-center">
          <CardTitle className="flex items-center gap-2">
            {isExpanded ? <ChevronDown className="h-5 w-5" /> : <ChevronRight className="h-5 w-5" />}
            <Brain className="h-5 w-5" />
            章节研究对话
          </CardTitle>
          {!isExpanded && renderCollapsedState()}
        </div>
        {isExpanded && (
          <CardDescription className="pt-2">
            {chapterGoal}
          </CardDescription>
        )}
      </CardHeader>

      {isExpanded && (
        <div className="space-y-6 px-6 pb-6">
          {/* 进度显示 */}
          {isActive && (
            <div className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-lg font-semibold">讨论进度</h4>
                  <div className="flex gap-2">
                    <Badge variant="outline">
                      第 {currentRound}/{maxRounds} 轮
                    </Badge>
                    {isNearChapterEnd() && (
                      <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                        ⚠️ 接近结束判断
                      </Badge>
                    )}
                  </div>
                </div>
                <p className="text-sm text-gray-500 mb-2">
                  当前: {currentAgent} • {isRunning ? '思考中...' : '等待中'}
                </p>
                <Progress value={getProgress()} className="w-full" />
              </div>

              {/* 阶段信息 */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <h5 className="font-medium">当前阶段</h5>
                  {isNearForced() && (
                    <Badge variant="secondary" className="bg-red-100 text-red-800">
                      ⚡ 即将强制推进
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-3">
                  <Badge className={getPhaseInfo().color}>
                    {getPhaseInfo().icon} {getPhaseInfo().name}
                  </Badge>
                  <span className="text-sm text-gray-500">
                    循环: {getPhaseInfo().count}
                  </span>
                  <div className="flex-1">
                    <Progress
                      value={getPhaseInfo().progress * 100}
                      className="w-full h-2"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {isActive && <Separator className="my-6" />}

          {/* 对话历史 */}
          {discussions.length > 0 && (
            <div>
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-lg font-semibold">Alpha-Beta 对话</h4>
                <Button variant="link" size="sm" onClick={() => setShowRawLog(!showRawLog)} className="text-xs">
                  {showRawLog ? '隐藏原始日志' : '</> 显示原始日志'}
                </Button>
              </div>
              <ScrollArea className="h-[400px] pr-4 border bg-gray-50/50 rounded-md p-4">
                {showRawLog ? (
                  <pre className="whitespace-pre-wrap text-xs text-gray-600 bg-black p-4 rounded-md text-white font-mono">
                    {discussions.map(d =>
                      `[第${d.round}轮-第${d.turn}次] ${d.agentId}\n思考: ${d.thought}\n动作: ${d.action.actionName}\n\n`
                    ).join('')}
                  </pre>
                ) : (
                  <div className="space-y-4">
                    {discussions.map((discussion, index) => (
                      <div key={index} className="border rounded-lg p-4 bg-white shadow-sm transition-all hover:shadow-md">
                        <div className="flex items-center gap-2 mb-3 pb-2 border-b">
                          {getActionIcon(discussion.action.actionName)}
                          <Badge variant="outline" className={`font-semibold ${getAgentColor(discussion.agentId)}`}>
                            {getAgentIcon(discussion.agentId)} {discussion.agentId}
                          </Badge>
                          <Badge variant="secondary" className={getActionColor(discussion.action.actionName)}>
                            {discussion.action.actionName}
                          </Badge>
                          <span className="text-xs text-gray-500 ml-auto">
                            第{discussion.round}轮 第{discussion.turn}次
                          </span>
                        </div>
                        
                        <div className="space-y-3 text-sm pl-1">
                          <div className="border-l-2 pl-4 border-gray-200">
                            <p className="font-medium text-gray-500 text-xs uppercase tracking-wider">思考过程</p>
                            <p className="mt-1 text-gray-800">{discussion.thought}</p>
                          </div>

                          {renderPayloadContent(discussion.action)}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </div>
          )}
        </div>
      )}
    </Card>
  );
}
