# 自动信息收集功能重构需求分析

## 1. 重构背景与目标

### 1.1 当前系统局限性

**架构局限**：
- 单一AI的反思-评估模式缺乏对抗性思考，容易陷入思维定势
- 轮次驱动的迭代方式导致上下文累积过载，影响后期决策质量
- 缺乏结构化的研究组织方式，难以保证研究计划的完整覆盖

**工作流局限**：
- 反思和评估阶段职责边界模糊，存在功能重叠
- 缺乏有效的纠错机制，一旦方向偏离难以及时调整
- 停止条件过于依赖AI主观判断，缺乏客观指标支撑

**上下文管理局限**：
- 随着轮次增加，上下文窗口被历史信息撑爆
- 无法实现真正的专注研究，容易被无关信息干扰
- 缺乏章节间的清晰边界和信息传递机制

### 1.2 新系统设计理念

**核心理念**："基于提纲的、隔离上下文的章节式迭代研究"

**关键特征**：
- **对称辩论**：两个完全对等的AI智能体进行批判性协作
- **章节驱动**：以研究计划的章节为独立研究单元
- **上下文隔离**：每章节开始时完全重置对话历史和知识库
- **共识机制**：通过结构化辩论达成高质量决策

### 1.3 预期收益

**研究质量提升**：
- 对抗性思考提高决策质量和创新性
- 章节式组织确保研究计划的完整覆盖
- 上下文隔离实现极致专注，避免信息干扰

**系统可扩展性**：
- 解决上下文过载问题，支持无限扩展的研究项目
- 模块化的章节设计便于并行处理和功能扩展
- 清晰的职责分离提高代码可维护性

**用户体验优化**：
- 透明的辩论过程增强决策可解释性
- 结构化的章节展示提供更好的信息组织
- 灵活的控制机制支持用户干预和调整

## 2. 架构对比分析

### 2.1 当前架构 vs 目标架构

| 维度 | 当前架构 | 目标架构 |
|------|----------|----------|
| **驱动单元** | 轮次(Round) | 章节(Chapter) |
| **AI模式** | 单一AI反思-评估 | 双AI对称辩论 |
| **上下文管理** | 累积历史信息 | 严格章节隔离 |
| **决策机制** | AI主观判断 | 结构化共识 |
| **工作流** | 线性轮次迭代 | 章节内多轮对话 |
| **扩展性** | 受上下文窗口限制 | 无限扩展能力 |

### 2.2 关键差异点分析

**工作流差异**：
```
当前：用户查询 → 生成计划 → 轮次1(搜索→反思→评估) → 轮次2... → 完成
目标：用户查询 → 生成计划 → 章节1(辩论→共识→查询) → 章节2... → 完成
```

**数据结构差异**：
```typescript
// 当前：轮次为核心
interface CurrentState {
  rounds: ResearchRound[];
  currentRound: number;
  reflection: ReflectionResult;
  evaluation: EvaluationResult;
}

// 目标：章节为核心
interface TargetState {
  chapters: ChapterState[];
  currentChapter: number;
  agentDebate: DebateHistory;
  consensus: ConsensusResult;
}
```

### 2.3 迁移路径规划

**阶段1：并行开发**
- 保留现有系统，新建独立的章节研究模块
- 实现核心的双AI辩论机制和上下文隔离

**阶段2：功能集成**
- 将新系统集成到现有界面
- 提供用户选择使用新旧系统的选项

**阶段3：全面替换**
- 基于用户反馈优化新系统
- 逐步废弃旧系统，完成迁移

## 3. 详细需求规格

### 3.1 功能需求

#### 3.1.1 双AI辩论机制
- **Agent定义**：两个完全对等的智能体(Alpha, Beta)
- **交互模式**：基于论点的对话，支持批判和反驳
- **动作空间**：CONTINUE_DISCUSSION, EXECUTE_QUERIES, CONCLUDE_CHAPTER
- **共识机制**：必须双方同意才能执行查询或结束章节

#### 3.1.2 章节式研究组织
- **章节解析**：自动从研究计划中提取章节结构
- **章节隔离**：每章节独立的上下文和知识库
- **章节切换**：完成当前章节后自动进入下一章节
- **进度跟踪**：实时显示章节完成状态和整体进度

#### 3.1.3 上下文隔离管理
- **严格隔离**：章节开始时完全重置对话历史
- **必要信息传递**：仅传递当前章节相关的研究目标
- **知识库管理**：每章节独立的知识积累和查询历史
- **边界控制**：防止跨章节信息泄露

#### 3.1.4 两阶段思考机制
- **战略回顾**：评估过往成果，确认研究方向
- **战术规划**：基于回顾结果制定具体行动计划
- **强制分离**：两阶段必须严格按顺序执行
- **输出规范**：每阶段都有明确的输出要求

### 3.2 非功能需求

#### 3.2.1 性能要求
- **响应时间**：单次AI对话响应时间 < 30秒
- **并发处理**：支持多个章节的并行处理
- **内存管理**：有效控制上下文隔离的内存开销
- **扩展性**：支持100+章节的大型研究项目

#### 3.2.2 可靠性要求
- **错误恢复**：AI分歧无法达成共识时的处理机制
- **状态一致性**：章节切换时的状态同步保证
- **数据持久化**：辩论历史和共识结果的可靠存储
- **异常处理**：网络中断、API失败等异常情况的处理

#### 3.2.3 可用性要求
- **界面直观**：清晰展示辩论过程和共识结果
- **操作简便**：支持用户干预和手动控制
- **信息透明**：完整记录决策过程和理由
- **配置灵活**：支持自定义章节划分和参数调整

### 3.3 接口需求

#### 3.3.1 API设计
```typescript
// 章节研究接口
interface ChapterResearchAPI {
  startChapterResearch(chapterGoal: string): Promise<void>;
  continueDebate(agentId: string, action: AgentAction): Promise<DebateResponse>;
  executeConsensusQueries(queries: ConsensusQuery[]): Promise<QueryResults>;
  concludeChapter(justification: string): Promise<ChapterSummary>;
}

// 上下文隔离接口
interface ContextIsolationAPI {
  createChapterContext(chapterGoal: string): ChapterContext;
  resetContext(chapterId: string): void;
  transferEssentialInfo(fromChapter: string, toChapter: string): void;
}
```

#### 3.3.2 数据格式
```typescript
// 辩论历史格式
interface DebateHistory {
  chapterId: string;
  discussions: Discussion[];
  consensus: ConsensusResult | null;
  knowledgeSummary: string;
}

// 共识结果格式
interface ConsensusResult {
  agreedQueries: ConsensusQuery[];
  justification: string;
  timestamp: number;
  participatingAgents: string[];
}
```

## 4. 技术实现方案

### 4.1 核心模块设计

#### 4.1.1 章节研究管理器 (ChapterResearchManager)
```typescript
class ChapterResearchManager {
  private chapters: Map<string, ChapterState>;
  private currentChapter: string | null;
  private contextIsolator: ContextIsolator;
  
  async startResearch(researchPlan: string): Promise<void>;
  async processChapter(chapterId: string): Promise<ChapterResult>;
  async switchToNextChapter(): Promise<void>;
}
```

#### 4.1.2 双AI辩论引擎 (AgentDebateEngine)
```typescript
class AgentDebateEngine {
  private agentAlpha: ResearchAgent;
  private agentBeta: ResearchAgent;
  private debateHistory: DebateHistory;
  
  async initiateDebate(chapterGoal: string): Promise<void>;
  async processAgentAction(agentId: string, action: AgentAction): Promise<DebateResponse>;
  async checkConsensus(): Promise<ConsensusResult | null>;
}
```

#### 4.1.3 上下文隔离器 (ContextIsolator)
```typescript
class ContextIsolator {
  private chapterContexts: Map<string, ChapterContext>;
  
  createIsolatedContext(chapterGoal: string): ChapterContext;
  resetContext(chapterId: string): void;
  transferEssentialInfo(fromChapter: string, toChapter: string): void;
}
```

### 4.2 数据结构定义

```typescript
// 章节状态
interface ChapterState {
  id: string;
  goal: string;
  status: 'pending' | 'in_progress' | 'completed';
  debateHistory: DebateHistory;
  consensus: ConsensusResult | null;
  knowledgeSummary: string;
  queryResults: QueryResult[];
}

// 辩论历史
interface DebateHistory {
  discussions: Discussion[];
  currentRound: number;
  totalTurns: number;
}

// 讨论记录
interface Discussion {
  round: number;
  turn: number;
  agentId: 'Alpha' | 'Beta';
  thought: string;
  speech: string;
  action: AgentAction;
  timestamp: number;
}

// 智能体动作
interface AgentAction {
  actionName: 'CONTINUE_DISCUSSION' | 'EXECUTE_QUERIES' | 'CONCLUDE_CHAPTER';
  payload: {
    rationale?: string;
    queries?: ConsensusQuery[];
    justification?: string;
  };
}
```

### 4.3 工作流设计

```mermaid
graph TD
    A[开始研究] --> B[解析研究计划]
    B --> C[提取章节列表]
    C --> D[创建章节上下文]
    D --> E[启动双AI辩论]
    E --> F{达成共识?}
    F -->|否| G[继续辩论]
    G --> E
    F -->|是| H[执行共识查询]
    H --> I[更新知识库]
    I --> J{章节完成?}
    J -->|否| E
    J -->|是| K[总结章节成果]
    K --> L{还有章节?}
    L -->|是| M[切换下一章节]
    M --> D
    L -->|否| N[完成研究]
```

## 5. 实施计划

### 5.1 开发阶段划分

**阶段1：核心架构开发 (4周)**
- 设计和实现核心数据结构
- 开发章节研究管理器
- 实现基础的上下文隔离机制

**阶段2：双AI辩论引擎 (3周)**
- 实现智能体定义和交互逻辑
- 开发共识机制和决策流程
- 集成两阶段思考机制

**阶段3：系统集成测试 (2周)**
- 集成各个模块
- 进行功能测试和性能优化
- 修复发现的问题

**阶段4：UI开发和用户测试 (3周)**
- 开发新的用户界面组件
- 进行用户体验测试
- 根据反馈进行优化

### 5.2 里程碑定义

- **M1**：核心架构完成，基础功能可用
- **M2**：双AI辩论机制完成，能够进行简单对话
- **M3**：完整系统集成，所有功能正常运行
- **M4**：用户界面完成，系统可以正式发布

### 5.3 风险评估与控制

**技术风险**：
- AI模型的稳定性和一致性问题
- 上下文隔离的实现复杂度
- 双AI可能无法达成共识的情况

**控制措施**：
- 充分的单元测试和集成测试
- 实现降级机制和异常处理
- 提供手动干预和调整功能

**项目风险**：
- 开发周期可能超出预期
- 用户接受度不确定
- 与现有系统的兼容性问题

**控制措施**：
- 采用敏捷开发方法，及时调整计划
- 早期进行用户测试和反馈收集
- 保持现有系统的稳定运行

### 5.4 资源需求估算

**开发资源**：
- 核心开发：2-3名高级开发工程师
- UI开发：1名前端工程师
- 测试：1名测试工程师

**时间资源**：
- 总开发周期：12周
- 并行开发可压缩至8-10周

**技术资源**：
- AI模型API调用成本增加（双AI机制）
- 服务器资源需求增加（上下文隔离存储）
- 开发和测试环境资源

---

*文档创建时间：2025-07-28*
*基于：当前auto-research系统分析和重构目标文档*
