// 章节式研究功能Hook
import { useState, useCallback, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

// 自定义hooks导入
import useDeepResearch from "@/hooks/useDeepResearch";
import useModelProvider from "@/hooks/useAiProvider";
import { useTaskStore } from "@/store/task";

// 类型和工具导入
import { ChapterResearchManager } from "@/utils/deep-research/chapter-research-manager";
import { getSystemPrompt } from "@/utils/deep-research/prompts";
import {
  ChapterInfo,
  ChapterState,
  Discussion,
  ConsensusQuery
} from "@/utils/deep-research/auto-research-prompts";

/**
 * 章节式研究功能Hook
 * 替换原有的反思思考机制，实现基于提纲的、隔离上下文的章节式迭代研究
 */
export function useChapterResearch() {
  const { t } = useTranslation();
  const getTaskStore = () => useTaskStore.getState();
  const { runSearchTask } = useDeepResearch();
  const { createModelProvider, getModel } = useModelProvider();

  // 本地状态
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentStatus, setCurrentStatus] = useState<string>("");
  const [chapterManager, setChapterManager] = useState<ChapterResearchManager | null>(null);

  // 调试isProcessing状态变化
  useEffect(() => {
    console.log("🔄 章节研究 isProcessing 状态变化:", isProcessing);
  }, [isProcessing]);
  
  // 中止控制器
  const abortControllerRef = useRef<AbortController | null>(null);

  /**
   * 获取动作图标
   */
  const getActionIcon = (actionName: string): string => {
    switch (actionName) {
      case 'CONTINUE_DISCUSSION':
        return '💬';
      case 'EXECUTE_QUERIES':
        return '🔍';
      case 'CONCLUDE_CHAPTER':
        return '✅';
      default:
        return '❓';
    }
  };

  /**
   * 格式化讨论学习内容
   */
  const formatDiscussionLearning = useCallback((discussion: Discussion): string => {
    const actionName = discussion.action.actionName;
    const payload = discussion.action.payload;
    const agentIcon = discussion.agentId === 'Alpha' ? '🤖' : '🔍';
    const actionIcon = getActionIcon(actionName);

    let content = `### ${agentIcon} **${discussion.agentId}** ${actionIcon} (第${discussion.turn}次发言)\n\n`;
    content += `💭 **思考过程**\n${discussion.thought}\n\n`;
    content += `🗣️ **发言内容**\n${discussion.speech}\n\n`;
    content += `⚡ **采取动作**\n${actionName}\n\n`;

    if (actionName === 'EXECUTE_QUERIES' && payload.queries) {
      content += `🔍 **提议查询**\n`;
      payload.queries.forEach((query: ConsensusQuery, index: number) => {
        content += `${index + 1}. ${query.query} (${query.language})\n   目标: ${query.researchGoal}\n`;
      });
    } else if (actionName === 'CONTINUE_DISCUSSION' && payload.rationale) {
      content += `📝 **继续理由**\n${payload.rationale}`;
    } else if (actionName === 'CONCLUDE_CHAPTER' && payload.justification) {
      content += `🏁 **结束理由**\n${payload.justification}`;
    }

    return content;
  }, []);

  /**
   * 添加章节开始任务卡片
   */
  const addChapterStartTask = useCallback((data: any) => {
    const task = {
      query: `📖 第${data.chapterIndex}章开始`,
      language: "chinese" as const,
      researchGoal: data.goal,
      learning: `🎯 **章节目标**\n${data.goal}\n\n📊 **进度**\n第 ${data.chapterIndex} 章 / 共 ${data.totalChapters} 章`,
      state: "completed" as const,
      sources: [],
      images: [],
      roundNumber: data.chapterIndex,
      taskType: "chapter_start" as const,
    };

    getTaskStore().addAutoResearchTask(task);
  }, []);

  /**
   * 添加或更新轮次讨论任务卡片
   */
  const addOrUpdateRoundDiscussion = useCallback((data: any) => {
    const { discussion }: { discussion: Discussion } = data;
    const taskStore = getTaskStore();

    const roundQuery = `💬 第${discussion.round}轮讨论`;

    // 查找是否已存在该轮次的讨论任务
    const existingTask = taskStore.tasks.find(
      task => task.query === roundQuery && task.taskType === "agent_discussion"
    );

    if (existingTask) {
      // 更新现有任务，添加新的讨论内容
      const newLearning = existingTask.learning + "\n\n---\n\n" + formatDiscussionLearning(discussion);

      // 更新任务
      taskStore.updateTask(roundQuery, {
        learning: newLearning,
        researchGoal: `第${discussion.round}轮讨论 (${discussion.turn}次对话)`
      });
    } else {
      // 创建新的轮次讨论任务
      const task = {
        query: roundQuery,
        language: "chinese" as const,
        researchGoal: `第${discussion.round}轮讨论 (${discussion.turn}次对话)`,
        learning: formatDiscussionLearning(discussion),
        state: "completed" as const,
        sources: [],
        images: [],
        roundNumber: discussion.round,
        taskType: "agent_discussion" as const,
      };

      taskStore.addAutoResearchTask(task);
    }
  }, [formatDiscussionLearning]);

  /**
   * 添加章节完成任务卡片
   */
  const addChapterCompletedTask = useCallback((data: any) => {
    const task = {
      query: `✅ 章节完成`,
      language: "chinese" as const,
      researchGoal: "章节研究总结",
      learning: `🎉 **章节完成**\n\n📝 **完成理由**\n${data.justification}\n\n📊 **统计信息**\n- 总轮次: ${data.totalRounds}\n- 总对话: ${data.totalTurns}`,
      state: "completed" as const,
      sources: [],
      images: [],
      roundNumber: data.totalRounds,
      taskType: "chapter_completed" as const,
    };

    getTaskStore().addAutoResearchTask(task);
  }, []);

  /**
   * 执行共识查询
   */
  const executeConsensusQueries = useCallback(async (queries: ConsensusQuery[]) => {
    try {
      setCurrentStatus(t("research.chapterResearch.executingQueries"));

      for (const consensusQuery of queries) {
        // 检查是否被取消
        if (abortControllerRef.current?.signal.aborted) {
          break;
        }

        // 创建搜索任务
        const searchTask = {
          query: consensusQuery.query,
          language: consensusQuery.language,
          researchGoal: consensusQuery.researchGoal,
          learning: "",
          state: "unprocessed" as const,
          sources: [],
          images: [],
          roundNumber: 1, // 这里可以根据实际章节轮次调整
          taskType: "search" as const,
        };

        // 添加到任务列表
        getTaskStore().addAutoResearchTask(searchTask);

        // 执行搜索
        await runSearchTask([searchTask]);
      }

    } catch (error) {
      console.error("执行共识查询失败:", error);
      toast.error(t("research.chapterResearch.queryExecutionFailed"));
    }
  }, [t, runSearchTask]);

  // 重复的函数定义已删除

  /**
   * 处理管理器消息
   */
  const handleManagerMessage = useCallback(async (type: string, data: any) => {
    switch (type) {
      case 'chapter_start':
        setCurrentStatus(t("research.chapterResearch.processingChapter", {
          chapter: data.chapterIndex,
          total: data.totalChapters
        }));

        // 添加章节开始任务卡片
        addChapterStartTask(data);
        break;

      case 'agent_thinking':
        setCurrentStatus(t("research.chapterResearch.agentThinking", {
          agent: data.agentId,
          round: data.round,
          turn: data.turn
        }));
        break;

      case 'agent_response':
        // 添加或更新轮次讨论任务卡片
        addOrUpdateRoundDiscussion(data);
        break;

      case 'consensus_reached':
        // 执行共识查询
        await executeConsensusQueries(data.queries);
        break;

      case 'chapter_completed':
        // 添加章节完成任务卡片
        addChapterCompletedTask(data);
        break;

      case 'research_completed':
        setCurrentStatus(t("research.chapterResearch.completed"));
        setIsProcessing(false);
        getTaskStore().setAutoResearchRunning(false);
        toast.success(t("research.chapterResearch.completedSuccessfully"));
        break;

      case 'agent_error':
        console.error(`Agent ${data.agentId} 错误:`, data.error);
        toast.error(t("research.chapterResearch.agentError", { agent: data.agentId }));
        // 停止研究
        setIsProcessing(false);
        getTaskStore().setAutoResearchRunning(false);
        setCurrentStatus(t("research.chapterResearch.stopped"));
        break;

      default:
        console.log(`未处理的管理器消息类型: ${type}`, data);
    }
  }, [t, addChapterStartTask, addOrUpdateRoundDiscussion, addChapterCompletedTask, executeConsensusQueries]);

  /**
   * 开始章节式研究
   */
  const startChapterResearch = useCallback(async () => {
    console.log("🚀 startChapterResearch 被调用");
    try {
      console.log("📝 设置处理状态为true");
      setIsProcessing(true);
      setCurrentStatus(t("research.chapterResearch.initializing"));

      console.log("📋 获取任务存储");
      const { reportPlan } = getTaskStore();
      console.log("📄 报告计划:", reportPlan ? "存在" : "不存在");

      if (!reportPlan) {
        console.log("❌ 没有报告计划");
        toast.error(t("research.chapterResearch.noPlan"));
        return;
      }

      console.log("🛑 创建中止控制器");
      // 创建中止控制器
      abortControllerRef.current = new AbortController();

      console.log("⚙️ 设置自动研究状态");
      // 设置自动研究状态
      getTaskStore().setAutoResearchRunning(true);

      console.log("🏗️ 开始创建章节研究管理器");
      // 创建章节研究管理器
      const manager = new ChapterResearchManager(
        reportPlan,
        handleManagerMessage,
        createModelProvider,
        getSystemPrompt,
        getModel
      );

      console.log("💾 设置章节管理器");
      setChapterManager(manager);

      console.log("🎯 开始章节研究");
      // 开始研究
      await manager.startChapterResearch();

    } catch (error) {
      console.error("❌ 章节研究启动失败:", error);
      toast.error(t("research.chapterResearch.startFailed"));
      setIsProcessing(false);
      getTaskStore().setAutoResearchRunning(false);
    }
  }, [t, createModelProvider, handleManagerMessage, getModel]);

  /**
   * 停止章节式研究
   */
  const stopChapterResearch = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    
    setIsProcessing(false);
    setCurrentStatus(t("research.chapterResearch.stopped"));
    getTaskStore().setAutoResearchRunning(false);
    toast.info(t("research.chapterResearch.stoppedByUser"));
  }, [t]);

  /**
   * 获取章节列表
   */
  const getChapterList = useCallback((): ChapterInfo[] => {
    return chapterManager?.getChapterList() || [];
  }, [chapterManager]);

  /**
   * 获取章节状态
   */
  const getChapterState = useCallback((chapterId: string): ChapterState | null => {
    return chapterManager?.getChapterState(chapterId) || null;
  }, [chapterManager]);

  return {
    // 状态
    isProcessing,
    currentStatus,
    
    // 主要功能
    startChapterResearch,
    stopChapterResearch,
    
    // 数据获取
    getChapterList,
    getChapterState,
  };
}
