"use client";
import { useState } from "react";
import { Setting<PERSON>, RotateCcw } from "lucide-react";
import { Button } from "@/components/Internal/Button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
// 移除Collapsible组件，使用简单的条件渲染

interface ChapterResearchConfig {
  maxHighLevelLoops: number;
  maxQueryLoops: number;
  roundThreshold: number;
  maxRounds: number;
}

interface ChapterConfigPanelProps {
  config: ChapterResearchConfig;
  onConfigChange: (config: ChapterResearchConfig) => void;
  disabled?: boolean;
}

const DEFAULT_CONFIG: ChapterResearchConfig = {
  maxHighLevelLoops: 3,
  maxQueryLoops: 3,
  roundThreshold: 2,
  maxRounds: 5
};

export default function ChapterConfigPanel({
  config,
  onConfigChange,
  disabled = false
}: ChapterConfigPanelProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [localConfig, setLocalConfig] = useState<ChapterResearchConfig>(config);

  const handleInputChange = (field: keyof ChapterResearchConfig, value: string) => {
    const numValue = parseInt(value);
    if (isNaN(numValue) || numValue < 1) return;
    
    const newConfig = { ...localConfig, [field]: numValue };
    setLocalConfig(newConfig);
    onConfigChange(newConfig);
  };

  const resetToDefault = () => {
    setLocalConfig(DEFAULT_CONFIG);
    onConfigChange(DEFAULT_CONFIG);
  };

  return (
    <Card className="w-full">
      <CardHeader
        className="cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            <CardTitle className="text-lg">章节研究配置</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            {!isOpen && (
              <div className="text-sm text-gray-500">
                {localConfig.maxHighLevelLoops}/{localConfig.maxQueryLoops}/{localConfig.roundThreshold}/{localConfig.maxRounds}
              </div>
            )}
            <div className={`transform transition-transform ${isOpen ? 'rotate-180' : ''}`}>
              ⌄
            </div>
          </div>
        </div>
        <CardDescription>
          配置章节研究的循环控制和结束条件参数
        </CardDescription>
      </CardHeader>

      {isOpen && (
        <CardContent className="space-y-6">
            {/* 循环控制参数 */}
            <div>
              <h4 className="font-medium mb-3 flex items-center gap-2">
                🔄 循环控制参数
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="maxHighLevelLoops">高层级最大循环次数</Label>
                  <Input
                    id="maxHighLevelLoops"
                    type="number"
                    min="1"
                    max="10"
                    value={localConfig.maxHighLevelLoops}
                    onChange={(e) => handleInputChange('maxHighLevelLoops', e.target.value)}
                    disabled={disabled}
                  />
                  <p className="text-xs text-gray-500">
                    Alpha和Beta在高层级讨论阶段的最大循环次数，达到后强制进入查询阶段
                  </p>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="maxQueryLoops">查询最大循环次数</Label>
                  <Input
                    id="maxQueryLoops"
                    type="number"
                    min="1"
                    max="10"
                    value={localConfig.maxQueryLoops}
                    onChange={(e) => handleInputChange('maxQueryLoops', e.target.value)}
                    disabled={disabled}
                  />
                  <p className="text-xs text-gray-500">
                    在查询规划阶段的最大循环次数，达到后强制执行查询
                  </p>
                </div>
              </div>
            </div>

            <Separator />

            {/* 章节控制参数 */}
            <div>
              <h4 className="font-medium mb-3 flex items-center gap-2">
                🏁 章节控制参数
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="roundThreshold">结束判断轮数阈值</Label>
                  <Input
                    id="roundThreshold"
                    type="number"
                    min="1"
                    max="20"
                    value={localConfig.roundThreshold}
                    onChange={(e) => handleInputChange('roundThreshold', e.target.value)}
                    disabled={disabled}
                  />
                  <p className="text-xs text-gray-500">
                    超过此轮数后开始AI智能判断是否结束章节
                  </p>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="maxRounds">最大轮数限制</Label>
                  <Input
                    id="maxRounds"
                    type="number"
                    min="1"
                    max="50"
                    value={localConfig.maxRounds}
                    onChange={(e) => handleInputChange('maxRounds', e.target.value)}
                    disabled={disabled}
                  />
                  <p className="text-xs text-gray-500">
                    章节讨论的最大轮数，达到后强制结束章节
                  </p>
                </div>
              </div>
            </div>

            <Separator />

            {/* 操作按钮 */}
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                💡 提示：参数修改后立即生效，建议在研究开始前调整
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={resetToDefault}
                disabled={disabled}
                className="flex items-center gap-2"
              >
                <RotateCcw className="h-4 w-4" />
                重置默认
              </Button>
            </div>

            {/* 参数说明 */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h5 className="font-medium text-blue-800 mb-2">参数说明</h5>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• <strong>循环控制</strong>：防止AI在某个阶段无限讨论，确保流程推进</li>
                <li>• <strong>强制推进</strong>：达到最大循环次数时自动选择合适的动作</li>
                <li>• <strong>智能结束</strong>：AI会评估章节目标达成情况决定是否结束</li>
                <li>• <strong>轮数限制</strong>：防止章节讨论过长，确保研究效率</li>
              </ul>
            </div>
        </CardContent>
      )}
    </Card>
  );
}
