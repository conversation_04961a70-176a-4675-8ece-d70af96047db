import { create } from "zustand";
import { persist } from "zustand/middleware";
import { pick } from "radash";
import { ReportType, CustomReportFormat, DEFAULT_CUSTOM_FORMATS } from "@/types/report";
import {
  AutoResearchState,
  DEFAULT_AUTO_RESEARCH_STATE,
  AutoResearchConfig,
  validateAutoResearchConfig,
} from "@/types/auto-research";

// 对抗机制相关类型定义
export interface AdversarialRound {
  roundNumber: number;
  persona: '架构师' | '批判者';
  thought: string;
  action: 'propose_plan' | 'critique_plan' | 'finish_discussion';
  plan?: Array<{
    section_title: string;
    summary: string;
  }>;
  critique?: string;
  finish_reason?: string;
  timestamp: number;
}

export interface AdversarialPlanState {
  rounds: AdversarialRound[];
  currentRound: number;
  maxRounds: number;
  mode: 'fixed' | 'auto';
  isActive: boolean;
  isRunning: boolean;
  currentPersona: '架构师' | '批判者';
  conversationText: string;
}

export const DEFAULT_ADVERSARIAL_STATE: AdversarialPlanState = {
  rounds: [],
  currentRound: 0,
  maxRounds: 5,
  mode: 'auto',
  isActive: false,
  isRunning: false,
  currentPersona: '架构师',
  conversationText: '',
};

export interface TaskStore {
  id: string;
  question: string;
  resources: Resource[];
  query: string;
  questions: string;
  feedback: string;
  reportPlan: string;
  suggestion: string;
  tasks: SearchTask[];
  requirement: string;
  title: string;
  finalReport: string;
  sources: Source[];
  images: ImageSource[];
  knowledgeGraph: string;
  // Report format related fields
  reportType: ReportType;
  customPrompt: string;
  customFormats: CustomReportFormat[];
  selectedCustomFormatId: string | null;
  // Auto research related fields
  autoResearch: AutoResearchState;
  // Adversarial plan optimization related fields (optional)
  adversarialPlan?: AdversarialPlanState;
}

interface TaskFunction {
  update: (tasks: SearchTask[]) => void;
  setId: (id: string) => void;
  setTitle: (title: string) => void;
  setSuggestion: (suggestion: string) => void;
  setRequirement: (requirement: string) => void;
  setQuery: (query: string) => void;
  updateTask: (query: string, task: Partial<SearchTask>) => void;
  removeTask: (query: string, language?: "chinese" | "english") => boolean;
  canDeleteTask: (query: string, language?: "chinese" | "english") => boolean;
  setQuestion: (question: string) => void;
  addResource: (resource: Resource) => void;
  updateResource: (id: string, resource: Partial<Resource>) => void;
  removeResource: (id: string) => boolean;
  updateQuestions: (questions: string) => void;
  updateReportPlan: (plan: string) => void;
  updateFinalReport: (report: string) => void;
  setSources: (sources: Source[]) => void;
  setImages: (images: Source[]) => void;
  setFeedback: (feedback: string) => void;
  updateKnowledgeGraph: (knowledgeGraph: string) => void;
  clear: () => void;
  reset: () => void;
  backup: () => TaskStore;
  restore: (taskStore: TaskStore) => void;
  // Report format related functions
  setReportType: (reportType: ReportType) => void;
  setCustomPrompt: (prompt: string) => void;
  addCustomFormat: (format: Omit<CustomReportFormat, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateCustomFormat: (id: string, format: Partial<CustomReportFormat>) => void;
  removeCustomFormat: (id: string) => void;
  setSelectedCustomFormatId: (id: string | null) => void;
  // Auto research related functions
  updateAutoResearchConfig: (config: Partial<AutoResearchConfig>) => void;
  setAutoResearchRunning: (isRunning: boolean) => void;
  setAutoResearchCurrentRound: (round: number) => void;
  // 轮次相关函数已被删除，现在使用章节研究机制
  setAutoResearchShowConfig: (show: boolean) => void;
  resetAutoResearch: () => void;
  addAutoResearchTask: (task: SearchTask) => void;
  // Adversarial plan optimization related functions
  initAdversarialPlan: (maxRounds: number, mode: 'fixed' | 'auto') => void;
  setAdversarialPlanRunning: (isRunning: boolean) => void;
  addAdversarialRound: (round: AdversarialRound) => void;
  updateAdversarialPersona: (persona: '架构师' | '批判者') => void;
  updateAdversarialConversationText: (text: string) => void;
  finishAdversarialPlan: (finalPlan: string) => void;
  resetAdversarialPlan: () => void;
}

const defaultValues: TaskStore = {
  id: "",
  question: "",
  resources: [],
  query: "",
  questions: "",
  feedback: "",
  reportPlan: "",
  suggestion: "",
  tasks: [],
  requirement: "",
  title: "",
  finalReport: "",
  sources: [],
  images: [],
  knowledgeGraph: "",
  // Report format related default values
  reportType: "standard",
  customPrompt: "",
  customFormats: DEFAULT_CUSTOM_FORMATS,
  selectedCustomFormatId: null,
  // Auto research related default values
  autoResearch: DEFAULT_AUTO_RESEARCH_STATE,
};

export const useTaskStore = create(
  persist<TaskStore & TaskFunction>(
    (set, get) => ({
      ...defaultValues,
      update: (tasks) => set(() => ({ tasks: [...tasks] })),
      setId: (id) => set(() => ({ id })),
      setTitle: (title) => set(() => ({ title })),
      setSuggestion: (suggestion) => set(() => ({ suggestion })),
      setRequirement: (requirement) => set(() => ({ requirement })),
      setQuery: (query) => set(() => ({ query })),
      updateTask: (query, task) => {
        const newTasks = get().tasks.map((item) => {
          return item.query === query ? { ...item, ...task } : item;
        });
        set(() => ({ tasks: [...newTasks] }));
      },
      removeTask: (query, language) => {
        set((state) => ({
          tasks: state.tasks.filter((task) => {
            // 如果指定了language，则同时匹配query和language
            if (language) {
              return !(task.query === query && task.language === language);
            }
            // 如果没有指定language，则只匹配query（向后兼容）
            return task.query !== query;
          }),
        }));
        return true;
      },
      canDeleteTask: (query, language) => {
        const task = get().tasks.find((task) => {
          if (language) {
            return task.query === query && task.language === language;
          }
          return task.query === query;
        });
        // 只有非processing状态的任务才能删除
        return task ? task.state !== "processing" : false;
      },
      setQuestion: (question) => set(() => ({ question })),
      addResource: (resource) =>
        set((state) => ({ resources: [resource, ...state.resources] })),
      updateResource: (id, resource) => {
        const newResources = get().resources.map((item) => {
          return item.id === id ? { ...item, ...resource } : item;
        });
        set(() => ({ resources: [...newResources] }));
      },
      removeResource: (id) => {
        set((state) => ({
          resources: state.resources.filter((resource) => resource.id !== id),
        }));
        return true;
      },
      updateQuestions: (questions) => set(() => ({ questions })),
      updateReportPlan: (plan) => set(() => ({ reportPlan: plan })),
      updateFinalReport: (report) => set(() => ({ finalReport: report })),
      setSources: (sources) => set(() => ({ sources })),
      setImages: (images) => set(() => ({ images })),
      setFeedback: (feedback) => set(() => ({ feedback })),
      updateKnowledgeGraph: (knowledgeGraph) => set(() => ({ knowledgeGraph })),
      clear: () => set(() => ({ tasks: [] })),
      reset: () => set(() => ({ ...defaultValues })),
      backup: () => {
        return {
          ...pick(get(), Object.keys(defaultValues) as (keyof TaskStore)[]),
        } as TaskStore;
      },
      restore: (taskStore) => set(() => ({ ...taskStore })),
      // Report format related function implementations
      setReportType: (reportType) => set(() => ({ reportType })),
      setCustomPrompt: (customPrompt) => set(() => ({ customPrompt })),
      addCustomFormat: (format) => {
        const newFormat: CustomReportFormat = {
          ...format,
          id: `custom-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        set((state) => ({
          customFormats: [...state.customFormats, newFormat],
        }));
      },
      updateCustomFormat: (id, format) => {
        set((state) => ({
          customFormats: state.customFormats.map((item) =>
            item.id === id
              ? { ...item, ...format, updatedAt: new Date().toISOString() }
              : item
          ),
        }));
      },
      removeCustomFormat: (id) => {
        set((state) => ({
          customFormats: state.customFormats.filter((format) => format.id !== id),
          selectedCustomFormatId:
            state.selectedCustomFormatId === id ? null : state.selectedCustomFormatId,
        }));
      },
      setSelectedCustomFormatId: (selectedCustomFormatId) =>
        set(() => ({ selectedCustomFormatId })),
      // Auto research related function implementations
      updateAutoResearchConfig: (config) => {
        set((state) => ({
          autoResearch: {
            ...state.autoResearch,
            config: validateAutoResearchConfig({
              ...state.autoResearch.config,
              ...config,
            }),
          },
        }));
      },
      setAutoResearchRunning: (isRunning) => {
        set((state) => ({
          autoResearch: {
            ...state.autoResearch,
            runtime: {
              ...state.autoResearch.runtime,
              isRunning,
              startTime: isRunning ? Date.now() : state.autoResearch.runtime.startTime,
            },
          },
        }));
      },
      setAutoResearchCurrentRound: (currentRound) => {
        set((state) => ({
          autoResearch: {
            ...state.autoResearch,
            runtime: {
              ...state.autoResearch.runtime,
              currentRound,
            },
          },
        }));
      },
      // 轮次相关函数实现已被删除
      setAutoResearchShowConfig: (showConfig) => {
        set((state) => ({
          autoResearch: {
            ...state.autoResearch,
            showConfig,
          },
        }));
      },
      resetAutoResearch: () => {
        set(() => ({
          autoResearch: DEFAULT_AUTO_RESEARCH_STATE,
        }));
      },
      addAutoResearchTask: (task) => {
        set((state) => ({
          tasks: [...state.tasks, task],
        }));
      },
      // Adversarial plan optimization related function implementations
      initAdversarialPlan: (maxRounds, mode) => {
        set(() => ({
          adversarialPlan: {
            ...DEFAULT_ADVERSARIAL_STATE,
            maxRounds,
            mode,
            isActive: true,
            currentPersona: '架构师',
          },
        }));
      },
      setAdversarialPlanRunning: (isRunning) => {
        set((state) => ({
          adversarialPlan: state.adversarialPlan ? {
            ...state.adversarialPlan,
            isRunning,
          } : undefined,
        }));
      },
      addAdversarialRound: (round) => {
        set((state) => ({
          adversarialPlan: state.adversarialPlan ? {
            ...state.adversarialPlan,
            rounds: [...state.adversarialPlan.rounds, round],
            currentRound: round.roundNumber,
          } : undefined,
        }));
      },
      updateAdversarialPersona: (persona) => {
        set((state) => ({
          adversarialPlan: state.adversarialPlan ? {
            ...state.adversarialPlan,
            currentPersona: persona,
          } : undefined,
        }));
      },
      updateAdversarialConversationText: (text) => {
        set((state) => ({
          adversarialPlan: state.adversarialPlan ? {
            ...state.adversarialPlan,
            conversationText: text,
          } : undefined,
        }));
      },
      finishAdversarialPlan: (finalPlan) => {
        set((state) => ({
          reportPlan: finalPlan,
          adversarialPlan: state.adversarialPlan ? {
            ...state.adversarialPlan,
            isRunning: false,
            // isActive 保持 true 以便显示最终结果
          } : undefined,
        }));
      },
      resetAdversarialPlan: () => {
        set(() => ({
          adversarialPlan: undefined,
        }));
      },
    }),
    { name: "research" }
  )
);
